<script setup lang="ts">
import { ref, computed, reactive, watch } from 'vue'
import { Modal } from 'ant-design-vue'
import {
  ImportOutlined,
  ExportOutlined,
  SyncOutlined,
  BarChartOutlined,
  SearchOutlined,
  PlusOutlined,
  DeleteOutlined,
  WarningOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue'

// 违禁词数据类型
interface ForbiddenWord {
  id: number
  word: string
  category: string
  platform: string
  severity: '高' | '中' | '低'
  addDate: string
}

// 检测结果类型
interface CheckResult {
  hasViolation: boolean
  foundWords: ForbiddenWord[]
  totalCount: number
}

// 违禁词列表
const forbiddenWords = ref<ForbiddenWord[]>([
  {
    id: 1,
    word: '最好的',
    category: '极限词',
    platform: 'Temu',
    severity: '高',
    addDate: '2024-01-15'
  },
  {
    id: 2,
    word: '第一',
    category: '极限词',
    platform: '通用',
    severity: '高',
    addDate: '2024-01-14'
  },
  {
    id: 3,
    word: '独家',
    category: '虚假宣传',
    platform: 'Amazon',
    severity: '中',
    addDate: '2024-01-13'
  },
  {
    id: 4,
    word: '包治百病',
    category: '医疗词汇',
    platform: '通用',
    severity: '高',
    addDate: '2024-01-12'
  }
])

// 检测文本
const checkText = ref('')
const checkResult = ref<CheckResult | null>(null)

// 新增违禁词表单
const formData = reactive({
  word: '',
  category: '极限词',
  platform: '通用',
  severity: '高' as '高' | '中' | '低'
})

// 搜索关键词
const searchKeyword = ref('')

// 分类选项
const categories = ['极限词', '虚假宣传', '医疗词汇', '政治敏感', '其他']
const platforms = ['通用', 'Temu', 'Amazon', 'eBay', '淘宝']
const severities = ['高', '中', '低']

// 表格列定义
const columns = [
  {
    title: '违禁词',
    dataIndex: 'word',
    key: 'word',
    width: 150
  },
  {
    title: '分类',
    dataIndex: 'category',
    key: 'category',
    width: 120
  },
  {
    title: '平台',
    dataIndex: 'platform',
    key: 'platform',
    width: 100
  },
  {
    title: '严重程度',
    dataIndex: 'severity',
    key: 'severity',
    width: 120
  },
  {
    title: '添加日期',
    dataIndex: 'addDate',
    key: 'addDate',
    width: 120
  },
  {
    title: '操作',
    key: 'actions',
    width: 100,
    fixed: 'right'
  }
]

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) => `共 ${total} 条记录，显示 ${range[0]}-${range[1]} 条`,
  pageSizeOptions: ['10', '20', '50', '100']
})

// 过滤后的违禁词列表
const filteredWords = computed(() => {
  if (!searchKeyword.value) return forbiddenWords.value

  const keyword = searchKeyword.value.toLowerCase()
  return forbiddenWords.value.filter(word =>
    word.word.toLowerCase().includes(keyword) ||
    word.category.toLowerCase().includes(keyword) ||
    word.platform.toLowerCase().includes(keyword)
  )
})

// 更新分页信息
const updatePagination = () => {
  pagination.total = filteredWords.value.length
}

// 监听过滤数据变化，更新分页
watch(filteredWords, () => {
  updatePagination()
  // 如果当前页超出了总页数，重置到第一页
  const totalPages = Math.ceil(pagination.total / pagination.pageSize)
  if (pagination.current > totalPages && totalPages > 0) {
    pagination.current = 1
  }
}, { immediate: true })

// 检测文本中的违禁词
const checkForbiddenWords = () => {
  if (!checkText.value.trim()) {
    checkResult.value = null
    return
  }

  const foundWords = forbiddenWords.value.filter(word =>
    checkText.value.includes(word.word)
  )

  checkResult.value = {
    hasViolation: foundWords.length > 0,
    foundWords: foundWords,
    totalCount: foundWords.length
  }
}

// 表单验证规则
const formRules = {
  word: [
    { required: true, message: '请输入违禁词', trigger: 'blur' },
    { min: 1, max: 50, message: '违禁词长度应在 1-50 个字符', trigger: 'blur' }
  ]
}

// 添加违禁词
const handleAdd = () => {
  if (!formData.word.trim()) return

  const newId = Math.max(...forbiddenWords.value.map(w => w.id)) + 1
  const newWord: ForbiddenWord = {
    id: newId,
    word: formData.word.trim(),
    category: formData.category,
    platform: formData.platform,
    severity: formData.severity,
    addDate: new Date().toISOString().split('T')[0]
  }

  forbiddenWords.value.unshift(newWord)

  // 重置表单
  Object.assign(formData, {
    word: '',
    category: '极限词',
    platform: '通用',
    severity: '高'
  })

  console.info('违禁词添加成功')
}

// 删除违禁词
const handleDelete = (record: ForbiddenWord) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除违禁词"${record.word}"吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    onOk: () => {
      const index = forbiddenWords.value.findIndex(w => w.id === record.id)
      if (index > -1) {
        forbiddenWords.value.splice(index, 1)
        console.info('违禁词删除成功')
      }
    }
  })
}

// 搜索功能
const handleSearch = () => {
  pagination.current = 1
}

// 重置搜索
const handleReset = () => {
  searchKeyword.value = ''
  pagination.current = 1
}

// 分页变化
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

// 批量导入
const handleImport = () => {
  console.info('批量导入违禁词')
  // 这里实现批量导入逻辑
}

// 导出词库
const handleExport = () => {
  console.info('导出违禁词库')
  // 这里实现导出逻辑
}

// 同步官方词库
const handleSync = () => {
  console.info('同步官方词库')
  // 这里实现同步逻辑
}

// 统计报告
const handleStats = () => {
  console.info('查看统计报告')
  // 这里实现统计逻辑
}

// 初始化
updatePagination()
</script>

<template>
  <div class="h-full flex flex-col bg-gray-50">
    <!-- 页面标题 -->
    <a-card class="mb-6 shadow-sm" :bordered="false">
      <template #title>
        <div class="flex items-center space-x-2">
          <span class="text-xl">🚫</span>
          <span class="text-lg font-semibold">违禁词管理</span>
        </div>
      </template>

      <!-- 顶部功能按钮 -->
      <div class="flex flex-wrap gap-3">
        <a-button type="primary" @click="handleImport">
          <template #icon>
            <ImportOutlined />
          </template>
          批量导入
        </a-button>

        <a-button @click="handleExport">
          <template #icon>
            <ExportOutlined />
          </template>
          导出词库
        </a-button>

        <a-button @click="handleSync">
          <template #icon>
            <SyncOutlined />
          </template>
          同步官方
        </a-button>

        <a-button @click="handleStats">
          <template #icon>
            <BarChartOutlined />
          </template>
          统计报告
        </a-button>
      </div>
    </a-card>

    <!-- 文本检测区域 -->
    <a-card class="mb-6 shadow-sm" :bordered="false">
      <template #title>
        <div class="flex items-center space-x-2">
          <SearchOutlined />
          <span class="text-lg font-semibold">违禁词检测</span>
        </div>
      </template>

      <div class="space-y-4">
        <a-form-item label="输入要检测的文本">
          <a-textarea
            v-model:value="checkText"
            @input="checkForbiddenWords"
            :rows="4"
            placeholder="请输入商品标题、描述等文本内容..."
            show-count
            :maxlength="1000"
          />
        </a-form-item>

        <div v-if="checkResult">
          <!-- 检测到违禁词 -->
          <a-alert
            v-if="checkResult.hasViolation"
            type="error"
            show-icon
            :message="`检测到 ${checkResult.totalCount} 个违禁词`"
            class="mb-4"
          >
            <template #icon>
              <WarningOutlined />
            </template>

            <template #description>
              <div class="space-y-2 mt-2">
                <div
                  v-for="word in checkResult.foundWords"
                  :key="word.id"
                  class="flex items-center justify-between bg-white p-3 rounded border"
                >
                  <div class="flex items-center space-x-2">
                    <a-tag color="red">{{ word.word }}</a-tag>
                    <span class="text-sm text-gray-500">{{ word.category }}</span>
                  </div>
                  <a-tag
                    :color="word.severity === '高' ? 'red' : word.severity === '中' ? 'orange' : 'default'"
                  >
                    {{ word.severity }}风险
                  </a-tag>
                </div>
              </div>
            </template>
          </a-alert>

          <!-- 文本安全 -->
          <a-alert
            v-else
            type="success"
            show-icon
            message="未检测到违禁词，文本安全"
          >
            <template #icon>
              <CheckCircleOutlined />
            </template>
          </a-alert>
        </div>
      </div>
    </a-card>

    <!-- 添加违禁词 -->
    <a-card class="mb-6 shadow-sm" :bordered="false">
      <template #title>
        <div class="flex items-center space-x-2">
          <PlusOutlined />
          <span class="text-lg font-semibold">添加违禁词</span>
        </div>
      </template>

      <a-form
        :model="formData"
        :rules="formRules"
        layout="inline"
        @finish="handleAdd"
      >
        <a-row :gutter="16" class="w-full">
          <a-col :span="6">
            <a-form-item
              label="违禁词"
              name="word"
              required
            >
              <a-input
                v-model:value="formData.word"
                placeholder="输入违禁词"
                :maxlength="50"
              />
            </a-form-item>
          </a-col>

          <a-col :span="4">
            <a-form-item label="分类">
              <a-select
                v-model:value="formData.category"
                placeholder="选择分类"
              >
                <a-select-option
                  v-for="cat in categories"
                  :key="cat"
                  :value="cat"
                >
                  {{ cat }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="4">
            <a-form-item label="平台">
              <a-select
                v-model:value="formData.platform"
                placeholder="选择平台"
              >
                <a-select-option
                  v-for="platform in platforms"
                  :key="platform"
                  :value="platform"
                >
                  {{ platform }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="4">
            <a-form-item label="严重程度">
              <a-select
                v-model:value="formData.severity"
                placeholder="选择程度"
              >
                <a-select-option
                  v-for="severity in severities"
                  :key="severity"
                  :value="severity"
                >
                  {{ severity }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="6">
            <a-form-item>
              <a-button type="primary" html-type="submit">
                <template #icon>
                  <PlusOutlined />
                </template>
                添加违禁词
              </a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 违禁词列表 -->
    <div class="flex-1 overflow-hidden">
      <a-card :bordered="false" class="h-full shadow-sm">
        <template #title>
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <span class="text-lg font-semibold">违禁词库</span>
              <a-tag color="blue">{{ filteredWords.length }} 条</a-tag>
            </div>

            <div class="flex items-center space-x-3">
              <a-input
                v-model:value="searchKeyword"
                placeholder="搜索违禁词..."
                style="width: 300px"
                @press-enter="handleSearch"
              >
                <template #prefix>
                  <SearchOutlined />
                </template>
              </a-input>

              <a-button @click="handleReset">
                重置
              </a-button>
            </div>
          </div>
        </template>

        <a-table
          :columns="columns"
          :data-source="filteredWords"
          :pagination="pagination"
          :scroll="{ x: 800 }"
          row-key="id"
          @change="handleTableChange"
        >
          <!-- 违禁词列 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'word'">
              <a-tag color="red">{{ record.word }}</a-tag>
            </template>

            <!-- 分类列 -->
            <template v-else-if="column.key === 'category'">
              <a-tag color="blue">{{ record.category }}</a-tag>
            </template>

            <!-- 平台列 -->
            <template v-else-if="column.key === 'platform'">
              <a-tag color="green">{{ record.platform }}</a-tag>
            </template>

            <!-- 严重程度列 -->
            <template v-else-if="column.key === 'severity'">
              <a-tag
                :color="record.severity === '高' ? 'red' : record.severity === '中' ? 'orange' : 'default'"
              >
                {{ record.severity }}风险
              </a-tag>
            </template>

            <!-- 操作列 -->
            <template v-else-if="column.key === 'actions'">
              <a-button
                type="primary"
                danger
                size="small"
                @click="handleDelete(record)"
              >
                <template #icon>
                  <DeleteOutlined />
                </template>
                删除
              </a-button>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>
  </div>
</template>

<style scoped>
:deep(.ant-table-thead > tr > th) {
  font-weight: 600;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 12px 16px;
}

:deep(.ant-pagination) {
  margin-top: 16px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}
</style>
