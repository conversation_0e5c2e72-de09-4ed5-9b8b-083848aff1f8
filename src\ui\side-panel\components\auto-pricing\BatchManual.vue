<script setup lang="ts">
import { ref, computed } from 'vue'
import { UploadOutlined, PlayCircleOutlined } from '@ant-design/icons-vue'
import { getSiteOptions } from '../../../../config/temuSites'

// 批量传说明书表单
const manualForm = ref({
  shop: '',
  site: '',
  stockThreshold: '',
  onSale: '',
  itemNo: '',
  skcFilter: '',
  manual: null
})

// 店铺选项
const shopOptions = [
  {
    value: 'ZenithCai',
    label: 'ZenithCai',
    avatar: 'https://img.cdnfe.com/supplier-public-tag/201365d418b/060d9c4f-71ff-4da0-8b98-c6c8a8221896_300x300.jpeg'
  }
]

// 站点选项
const siteOptions = computed(() => getSiteOptions().map(site => ({
  value: site.label,
  label: `${site.label}站`
})))

// 是否在售选项
const onSaleOptions = [
  { value: '是', label: '是' },
  { value: '否', label: '否' }
]

// 选择说明书文件
const selectManual = () => {
  console.log('选择说明书文件')
}

// 开始上传说明书
const startUploadManual = () => {
  console.log('开始上传说明书:', manualForm.value)
  alert('批量传说明书功能已启动！')
}

// 文件上传处理
const handleFileChange = (info: any) => {
  console.log('文件上传:', info)
}
</script>

<template>
  <div>
    <a-form
      :model="manualForm"
      layout="vertical"
      @finish="startUploadManual"
      class="space-y-6"
    >
      <!-- 基础配置 -->
      <a-card title="基础配置" class="mb-6">
        <a-row :gutter="16">
          <!-- 店铺选择 -->
          <a-col :span="8">
            <a-form-item label="店铺">
              <a-select
                v-model:value="manualForm.shop"
                placeholder="选择店铺"
              >
                <a-select-option
                  v-for="shop in shopOptions"
                  :key="shop.value"
                  :value="shop.value"
                >
                  <div class="flex items-center space-x-2">
                    <img
                      :src="shop.avatar"
                      alt="店铺头像"
                      class="w-4 h-4 rounded"
                    />
                    <span>{{ shop.label }}</span>
                  </div>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 站点选择 -->
          <a-col :span="8">
            <a-form-item label="站点">
              <a-select
                v-model:value="manualForm.site"
                placeholder="选择站点"
              >
                <a-select-option
                  v-for="site in siteOptions"
                  :key="site.value"
                  :value="site.value"
                >
                  {{ site.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 是否在售 -->
          <a-col :span="8">
            <a-form-item label="是否在售">
              <a-select
                v-model:value="manualForm.onSale"
                placeholder="选择在售状态"
              >
                <a-select-option
                  v-for="option in onSaleOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 库存阈值和货号 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="库存阈值">
              <a-input-number
                v-model:value="manualForm.stockThreshold"
                placeholder="输入库存阈值"
                class="w-full"
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="货号">
              <a-input
                v-model:value="manualForm.itemNo"
                placeholder="输入货号"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- SKC筛选 -->
        <a-form-item label="SKC筛选">
          <a-input
            v-model:value="manualForm.skcFilter"
            placeholder="多个查询请英文逗号、空格依次输入(选填)"
          />
        </a-form-item>
      </a-card>

      <!-- 文件上传 -->
      <a-card title="说明书上传" class="mb-6">
        <a-form-item label="选择说明书文件">
          <a-upload
            :file-list="[]"
            :before-upload="() => false"
            @change="handleFileChange"
          >
            <a-button>
              <template #icon>
                <UploadOutlined />
              </template>
              选择文件
            </a-button>
          </a-upload>
          <div class="text-sm text-gray-500 mt-2">
            支持 PDF、DOC、DOCX 格式，文件大小不超过 10MB
          </div>
        </a-form-item>
      </a-card>

      <!-- 上传说明 -->
      <a-card title="上传说明" class="mb-6">
        <a-alert
          type="info"
          show-icon
          message="批量上传说明"
          description="系统将根据设置的条件批量为商品上传说明书文件，请确保文件格式正确且内容完整。"
        />
      </a-card>

      <!-- 提交按钮 -->
      <div class="flex justify-end">
        <a-button 
          type="primary"
          html-type="submit"
          size="large"
        >
          <template #icon>
            <PlayCircleOutlined />
          </template>
          开始批量上传
        </a-button>
      </div>
    </a-form>
  </div>
</template>
