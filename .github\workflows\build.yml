name: build
on:
  push:
    branches:
      - master
jobs:
  build_and_deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 23

      - run: npm ci
        env:
          PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: 1
      - run: npm run lint
      - run: npm run build

      - uses: akhilmhdh/contributors-readme-action@v2.3.10
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
