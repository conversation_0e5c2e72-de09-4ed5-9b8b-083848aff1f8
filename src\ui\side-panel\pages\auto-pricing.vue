<script setup lang="ts">
import { ref } from 'vue'
import { SettingOutlined } from '@ant-design/icons-vue'

// 导入子组件
import AutoPricingCost from '../components/auto-pricing/AutoPricingCost.vue'
import AutoDelist from '../components/auto-pricing/AutoDelist.vue'
import BatchManual from '../components/auto-pricing/BatchManual.vue'
import DownloadActivity from '../components/auto-pricing/DownloadActivity.vue'
import AutoActivity from '../components/auto-pricing/AutoActivity.vue'
import AutoSyncStock from '../components/auto-pricing/AutoSyncStock.vue'
import AutoPricingFixed from '../components/auto-pricing/AutoPricingFixed.vue'
import AutoFollowPrice from '../components/auto-pricing/AutoFollowPrice.vue'
import BatchChangeCode from '../components/auto-pricing/BatchChangeCode.vue'
import BatchCompliance from '../components/auto-pricing/BatchCompliance.vue'
import BatchAd from '../components/auto-pricing/BatchAd.vue'
import JitStock from '../components/auto-pricing/JitStock.vue'

// 定义事件
const emit = defineEmits<{
  close: []
}>()

// 当前选中的功能
const activeFunction = ref('auto-pricing-cost')

// 左侧菜单项
const menuItems = ref([
  {
    id: 'auto-sync-stock',
    name: '自动同步库存',
    icon: 'https://img.tglmall.com.cn/damaio/icon/58social.png'
  },
  {
    id: 'auto-pricing-cost',
    name: '自动核价成本',
    icon: 'https://img.tglmall.com.cn/icon/7social.png'
  },
  {
    id: 'auto-pricing-fixed',
    name: '自动核价固定(免费)',
    icon: 'https://img.tglmall.com.cn/icon/72social.png'
  },
  {
    id: 'auto-activity',
    name: '自动报名活动',
    icon: 'https://img.tglmall.com.cn/icon/69social.png'
  },
  {
    id: 'auto-follow-price',
    name: '自动跟价管理',
    icon: 'https://img.tglmall.com.cn/icon/135social.png'
  },
  {
    id: 'auto-delist',
    name: '自动下架商品',
    icon: 'https://img.tglmall.com.cn/icon/45social.png'
  },
  {
    id: 'jit-stock',
    name: 'JIT&库存管理',
    icon: 'https://img.tglmall.com.cn/damaio/icon/27social.png'
  },
  {
    id: 'batch-manual',
    name: '批量传说明书',
    icon: 'https://img.tglmall.com.cn/icon/8social.png'
  },
  {
    id: 'batch-change-code',
    name: '批量修改货号',
    icon: 'https://img.tglmall.com.cn/icon/78social.png'
  },
  {
    id: 'download-activity',
    name: '下载活动数据(新)',
    icon: 'https://img.tglmall.com.cn/damaio/icon/62social.png'
  },
  {
    id: 'batch-compliance',
    name: '批量合规中心',
    icon: 'https://img.tglmall.com.cn/damaio/icon/27social.png'
  },
  {
    id: 'batch-ad',
    name: '批量添加广告',
    icon: 'https://img.tglmall.com.cn/damaio/icon/85social.png'
  }
])

// 切换菜单项
const switchFunction = (functionId: string) => {
  activeFunction.value = functionId
}

// 关闭弹窗
const closeModal = () => {
  emit('close')
}




</script>

<template>
  <a-modal
    :open="true"
    title="店铺维护"
    :width="1400"
    :footer="null"
    :closable="true"
    @cancel="closeModal"
    class="auto-pricing-modal"
  >
    <div class="flex h-[80vh] overflow-hidden">
      <!-- 左侧菜单 -->
      <div class="w-64 border-r border-gray-200 overflow-y-auto">
        <a-menu
          :selected-keys="[activeFunction]"
          mode="inline"
          @click="({ key }) => switchFunction(key)"
          class="border-0"
        >
          <a-menu-item
            v-for="item in menuItems"
            :key="item.id"
          >
            <template #icon>
              <img :src="item.icon" alt="" class="w-4 h-4" />
            </template>
            <span class="text-sm">{{ item.name }}</span>
          </a-menu-item>
        </a-menu>
      </div>

      <!-- 右侧内容区域 -->
      <div class="flex-1 overflow-y-auto p-6">
        <!-- 自动核价成本配置 -->
        <AutoPricingCost v-if="activeFunction === 'auto-pricing-cost'" />

        <!-- 自动下架商品 -->
        <AutoDelist v-else-if="activeFunction === 'auto-delist'" />

        <!-- 批量传说明书 -->
        <BatchManual v-else-if="activeFunction === 'batch-manual'" />

        <!-- 下载活动数据 -->
        <DownloadActivity v-else-if="activeFunction === 'download-activity'" />

        <!-- 自动报名活动 -->
        <AutoActivity v-else-if="activeFunction === 'auto-activity'" />

        <!-- 自动同步库存 -->
        <AutoSyncStock v-else-if="activeFunction === 'auto-sync-stock'" />

        <!-- 自动核价固定 -->
        <AutoPricingFixed v-else-if="activeFunction === 'auto-pricing-fixed'" />

        <!-- 自动跟价管理 -->
        <AutoFollowPrice v-else-if="activeFunction === 'auto-follow-price'" />

        <!-- 批量修改货号 -->
        <BatchChangeCode v-else-if="activeFunction === 'batch-change-code'" />

        <!-- 批量合规中心 -->
        <BatchCompliance v-else-if="activeFunction === 'batch-compliance'" />

        <!-- 批量添加广告 -->
        <BatchAd v-else-if="activeFunction === 'batch-ad'" />

        <!-- JIT&库存管理 -->
        <JitStock v-else-if="activeFunction === 'jit-stock'" />
      </div>
    </div>
  </a-modal>
</template>







<style scoped>
/* 确保弹窗在最上层 */
.z-50 {
  z-index: 50;
}
</style>
