// 商品配置服务
// 处理店小秘商品配置的保存和读取

export interface ProductConfig {
  apiConfig: {
    erp: string
    imageSize: number
    platform: string
    pointKey: string
    shippingFrom: Record<string, string[]>
    txt: string
    url: string
  }
  data: {
    attributes: string
    categoryId: number
    categoryType: string
    draftImgUrl: string
    dxmPdfUrl: string
    dxmState: string
    freightTemplateId: string
    fullCid: string
    goodsModel: string
    id: number | null
    instructionsId: string
    instructionsName: string
    instructionsTranslateId: string
    op: number
    optionValue: string
    outerGoodsUrl: string
    packageImages: string
    packageShape: string
    packageType: string
    productId: number | null
    productOrigin: string
    productSemiManagedReq: string
    productWarehouseRouteReq: Array<{
      siteIdList: number[]
      warehouseId: string
    }>
    qualifiedEn: string
    region2Id: string
    sensitiveAttr: string
    shipmentLimitSecond: string
    shopId: string
    shopInfo: {
      dXmId: string
      fromPlat: string
      ideaCurrency: string
      shopId: number
      shopName: string
    }
    sizeTemplateIds: string
  }
  form: {
    attributes: string
    categoryId: number
    categoryName: string
    categoryType: number
    certificationInfo: Record<string, any>
    freightTemplateId: string
    op: number
    shipmentLimitSecond: string
    shopId: string
    siteId: string
    siteIds: string[]
    warehouseId: string[]
  }
  siteConfig: {
    currentSite: string
    erp: string
  }
  userInfo: {
    exp: number | null
    gender: string | null
    income: number | null
    level: number
    mid: number
    name: string
    nextExp: number | null
    pay: number | null
    phone: string
    photo: string
    pid: number | null
    pname: string
    point: number | null
    pphoto: string
    rankName: string
    stock: number | null
    token: string
    username: string
  }
  timestamp: string
  categoryPath: string
  shopId: string
}

class ProductConfigService {
  private readonly STORAGE_PREFIX = 'temu_product_config_'

  // 保存商品配置
  async saveProductConfig(shopId: string, categoryId: number, config: ProductConfig): Promise<void> {
    try {
      const key = `${this.STORAGE_PREFIX}${shopId}_${categoryId}`
      
      const configData = {
        ...config,
        timestamp: new Date().toISOString(),
        shopId,
        categoryId
      }

      await chrome.storage.local.set({
        [key]: configData
      })

      console.info('[ProductConfigService] 商品配置已保存:', {
        key,
        shopId,
        categoryId,
        categoryPath: config.categoryPath
      })
    } catch (error) {
      console.error('[ProductConfigService] 保存商品配置失败:', error)
      throw new Error('保存商品配置失败: ' + (error instanceof Error ? error.message : '未知错误'))
    }
  }

  // 获取商品配置
  async getProductConfig(shopId: string, categoryId: number): Promise<ProductConfig | null> {
    try {
      const key = `${this.STORAGE_PREFIX}${shopId}_${categoryId}`
      const result = await chrome.storage.local.get([key])
      
      if (result[key]) {
        console.info('[ProductConfigService] 获取商品配置成功:', {
          key,
          shopId,
          categoryId
        })
        return result[key] as ProductConfig
      }

      return null
    } catch (error) {
      console.error('[ProductConfigService] 获取商品配置失败:', error)
      return null
    }
  }

  // 获取所有商品配置
  async getAllProductConfigs(): Promise<Record<string, ProductConfig>> {
    try {
      const result = await chrome.storage.local.get(null)
      const configs: Record<string, ProductConfig> = {}

      Object.keys(result).forEach(key => {
        if (key.startsWith(this.STORAGE_PREFIX)) {
          configs[key] = result[key] as ProductConfig
        }
      })

      console.info('[ProductConfigService] 获取所有商品配置成功:', Object.keys(configs).length)
      return configs
    } catch (error) {
      console.error('[ProductConfigService] 获取所有商品配置失败:', error)
      return {}
    }
  }

  // 删除商品配置
  async deleteProductConfig(shopId: string, categoryId: number): Promise<void> {
    try {
      const key = `${this.STORAGE_PREFIX}${shopId}_${categoryId}`
      await chrome.storage.local.remove([key])
      
      console.info('[ProductConfigService] 商品配置已删除:', {
        key,
        shopId,
        categoryId
      })
    } catch (error) {
      console.error('[ProductConfigService] 删除商品配置失败:', error)
      throw new Error('删除商品配置失败: ' + (error instanceof Error ? error.message : '未知错误'))
    }
  }

  // 清空所有商品配置
  async clearAllProductConfigs(): Promise<void> {
    try {
      const result = await chrome.storage.local.get(null)
      const keysToRemove = Object.keys(result).filter(key => 
        key.startsWith(this.STORAGE_PREFIX)
      )

      if (keysToRemove.length > 0) {
        await chrome.storage.local.remove(keysToRemove)
        console.info('[ProductConfigService] 已清空所有商品配置:', keysToRemove.length)
      }
    } catch (error) {
      console.error('[ProductConfigService] 清空商品配置失败:', error)
      throw new Error('清空商品配置失败: ' + (error instanceof Error ? error.message : '未知错误'))
    }
  }

  // 检查是否存在商品配置
  async hasProductConfig(shopId: string, categoryId: number): Promise<boolean> {
    try {
      const config = await this.getProductConfig(shopId, categoryId)
      return config !== null
    } catch (error) {
      console.error('[ProductConfigService] 检查商品配置失败:', error)
      return false
    }
  }

  // 获取配置统计信息
  async getConfigStats(): Promise<{
    totalConfigs: number
    configsByShop: Record<string, number>
    latestConfig?: ProductConfig
  }> {
    try {
      const configs = await this.getAllProductConfigs()
      const configsByShop: Record<string, number> = {}
      let latestConfig: ProductConfig | undefined
      let latestTimestamp = ''

      Object.values(configs).forEach(config => {
        // 统计每个店铺的配置数量
        if (config.shopId) {
          configsByShop[config.shopId] = (configsByShop[config.shopId] || 0) + 1
        }

        // 找到最新的配置
        if (config.timestamp && config.timestamp > latestTimestamp) {
          latestTimestamp = config.timestamp
          latestConfig = config
        }
      })

      return {
        totalConfigs: Object.keys(configs).length,
        configsByShop,
        latestConfig
      }
    } catch (error) {
      console.error('[ProductConfigService] 获取配置统计失败:', error)
      return {
        totalConfigs: 0,
        configsByShop: {}
      }
    }
  }
}

// 导出单例实例
const productConfigService = new ProductConfigService()
export default productConfigService
