// Temu 店铺信息检测器
// 这个脚本会在 Temu 商家后台页面中运行，主动获取店铺信息并保存到 Chrome Storage

interface TemuSiteInfo {
  fromPlat: string
  mallId: string | number
  shopId: string | number
  mallName: string
  shopName: string
  mallStatus: number
  isSemiManagedMall: boolean
  logo: string
}

class TemuShopDetector {
  private shopInfo: TemuSiteInfo | null = null
  private isProcessing = false
  private maxRetries = 3
  private retryCount = 0

  constructor() {
    this.init()
  }

  private init() {
    console.info('[Temu Detector] 初始化检测器...')

    // 等待页面加载完成后开始检测
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.startDetection())
    } else {
      // 页面已加载，延迟一下再开始检测，确保页面完全渲染
      setTimeout(() => this.startDetection(), 1000)
    }
  }

  private async startDetection() {
    if (this.isProcessing) {
      console.info('[Temu Detector] 检测正在进行中，跳过...')
      return
    }

    this.isProcessing = true
    console.info('[Temu Detector] 开始检测店铺信息...')

    try {
      // 主动调用 Temu API 获取用户信息
      const result = await this.fetchTemuUserInfo()

      if (result) {
        this.shopInfo = result
        await this.saveToStorage(result)
        this.notifyExtension(result)
        console.info('[Temu Detector] 成功检测并保存店铺信息:', result)
      } else {
        // 如果失败，尝试重试
        this.retryDetection()
      }
    } catch (error) {
      console.error('[Temu Detector] 检测过程中出错:', error)
      this.retryDetection()
    } finally {
      this.isProcessing = false
    }
  }

  private async retryDetection() {
    if (this.retryCount >= this.maxRetries) {
      console.warn('[Temu Detector] 达到最大重试次数，停止检测')
      return
    }

    this.retryCount++
    console.info(`[Temu Detector] 第 ${this.retryCount} 次重试检测...`)

    setTimeout(() => {
      this.startDetection()
    }, 3000 * this.retryCount) // 递增延迟：3秒、6秒、9秒
  }

  // 获取页面中的 anti-content 头部
  public getAntiContentHeader(): string | null {
    try {
      // 尝试从页面的全局变量或元素中获取 anti-content
      // 这个值通常在页面加载时由 Temu 的 JS 生成
      const scripts = Array.from(document.querySelectorAll('script'))
      for (const script of scripts) {
        const content = script.textContent || script.innerHTML
        // 查找可能包含 anti-content 的模式
        const match = content.match(/["']anti-content["']\s*:\s*["']([^"']+)["']/i)
        if (match) {
          return match[1]
        }
      }

      // 尝试从 window 对象中获取
      const win = window as unknown as Record<string, unknown>
      const initialState = win.__INITIAL_STATE__ as Record<string, unknown> | undefined
      if (initialState && typeof initialState.antiContent === 'string') {
        return initialState.antiContent
      }

      return null
    } catch (error) {
      console.warn('[Temu Detector] 无法获取 anti-content 头部:', error)
      return null
    }
  }

  // 主动调用 Temu API 获取用户信息
  private async fetchTemuUserInfo(): Promise<TemuSiteInfo | null> {
    try {
      console.info('[Temu Detector] 主动调用 Temu API...')

      // 获取 anti-content 头部
      const antiContent = this.getAntiContentHeader()
      console.info('[Temu Detector] Anti-content 头部:', antiContent ? '已获取' : '未找到')

      // 构建请求头
      const headers: Record<string, string> = {
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Content-Type': 'application/json',
        'Cache-Control': 'max-age=0',
        'Origin': 'https://seller.kuajingmaihuo.com',
        'Referer': 'https://seller.kuajingmaihuo.com/',
        'Sec-Ch-Ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': navigator.userAgent
      }

      // 如果找到了 anti-content，添加到请求头中
      if (antiContent) {
        headers['anti-content'] = antiContent
      }

      // 调用 Temu 的用户信息 API
      const response = await fetch('https://seller.kuajingmaihuo.com/bg/quiet/api/mms/userInfo', {
        method: 'POST', // 改为 POST 请求，因为正常请求是 POST
        credentials: 'include', // 包含 cookies
        headers,
        body: JSON.stringify({}) // 发送空的 JSON 对象
      })

      console.info('[Temu Detector] API 响应状态:', response.status, response.statusText)

      if (response.ok) {
        const data = await response.json()
        console.info('[Temu Detector] API 返回数据:', data)

        const shopInfo = this.parseShopData(data)
        if (shopInfo) {
          console.info('[Temu Detector] 成功解析店铺信息:', shopInfo)
          return shopInfo
        } else {
          console.warn('[Temu Detector] 无法从 API 数据中解析店铺信息')
        }
      } else {
        console.warn('[Temu Detector] API 调用失败:', response.status, response.statusText)
      }
    } catch (error) {
      console.error('[Temu Detector] API 调用出错:', error)
    }

    return null
  }

  // 保存店铺信息到 Chrome Storage
  private async saveToStorage(shopInfo: TemuSiteInfo): Promise<void> {
    try {
      console.info('[Temu Detector] 开始保存店铺信息到存储...')

      if (typeof chrome !== 'undefined' && chrome.storage) {
        // 保存到 Chrome Storage
        await chrome.storage.local.set({
          temuSiteInfo: shopInfo,
          shop_binding_state: {
            isTemuBound: true,
            temuSiteInfo: shopInfo,
            lastCheckTime: new Date().toISOString()
          }
        })
        console.info('[Temu Detector] 数据已保存到 Chrome Storage')
      } else {
        // 降级到 localStorage
        localStorage.setItem('temuSiteInfo', JSON.stringify(shopInfo))
        localStorage.setItem('shop_binding_state', JSON.stringify({
          isTemuBound: true,
          temuSiteInfo: shopInfo,
          lastCheckTime: new Date().toISOString()
        }))
        console.info('[Temu Detector] 数据已保存到 LocalStorage')
      }
    } catch (error) {
      console.error('[Temu Detector] 保存数据失败:', error)
    }
  }

  // 解析 Temu API 返回的数据
  private parseShopData(data: unknown): TemuSiteInfo | null {
    if (!data || typeof data !== 'object') return null

    const apiData = data as Record<string, unknown>

    // 处理 Temu API 的标准返回格式
    // 格式: { success: true, result: { companyList: [{ malInfoList: [...] }] } }
    if (apiData.success && apiData.result) {
      const result = apiData.result as Record<string, unknown>

      // 检查 companyList 中的 malInfoList
      if (result.companyList && Array.isArray(result.companyList)) {
        for (const company of result.companyList) {
          if (company && typeof company === 'object') {
            const companyData = company as Record<string, unknown>
            if (companyData.malInfoList && Array.isArray(companyData.malInfoList)) {
              for (const mallInfo of companyData.malInfoList) {
                if (mallInfo && typeof mallInfo === 'object') {
                  const mallData = mallInfo as Record<string, unknown>
                  if (mallData.mallId && mallData.mallName) {
                    return {
                      fromPlat: 'temu',
                      mallId: mallData.mallId as string | number,
                      shopId: mallData.mallId as string | number, // 在 Temu 中 mallId 就是 shopId
                      mallName: mallData.mallName as string,
                      shopName: mallData.mallName as string,
                      mallStatus: (mallData.mallStatus as number) || 1,
                      isSemiManagedMall: (mallData.isSemiManagedMall as boolean) || false,
                      logo: (mallData.logo as string) || ''
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    console.warn('[Temu Detector] 无法解析 API 数据格式')
    return null
  }

  // 通知扩展程序检测到店铺信息
  private notifyExtension(shopInfo: TemuSiteInfo): void {
    console.info('[Temu Detector] 通知扩展程序检测到店铺信息...')

    // 通过 postMessage 发送消息
    window.postMessage({
      type: 'TEMU_SHOP_DETECTED',
      data: shopInfo
    }, '*')

    // 通过 chrome.runtime.sendMessage 发送给背景脚本
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.sendMessage({
        type: 'TEMU_SHOP_DETECTED',
        data: shopInfo
      }).catch(() => {
        // 忽略错误，可能是在非扩展环境中
        console.warn('[Temu Detector] 无法发送消息到背景脚本')
      })
    }
  }

  // 公共方法：手动触发检测
  public async manualDetect(): Promise<TemuSiteInfo | null> {
    console.info('[Temu Detector] 手动触发检测...')
    this.retryCount = 0
    return await this.fetchTemuUserInfo()
  }

  // 公共方法：获取原始 API 数据（用于 side panel 通信）
  public async getRawApiData(): Promise<unknown> {
    try {
      console.info('[Temu Detector] 获取原始 API 数据...')

      // 获取 anti-content 头部
      const antiContent = this.getAntiContentHeader()
      console.info('[Temu Detector] Anti-content 头部:', antiContent ? '已获取' : '未找到')

      // 构建请求头
      const headers: Record<string, string> = {
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Content-Type': 'application/json',
        'Cache-Control': 'max-age=0'
      }

      // 如果找到了 anti-content，添加到请求头中
      if (antiContent) {
        headers['anti-content'] = antiContent
      }

      const response = await fetch('https://seller.kuajingmaihuo.com/bg/quiet/api/mms/userInfo', {
        method: 'POST',
        credentials: 'include',
        headers,
        body: JSON.stringify({})
      })

      if (response.ok) {
        const data = await response.json()
        console.info('[Temu Detector] 原始 API 响应:', data)

        if (data.success && data.result) {
          return data.result // 返回原始的 result 数据
        } else {
          throw new Error('API 返回数据格式错误')
        }
      } else {
        throw new Error(`API 请求失败: ${response.status} ${response.statusText}`)
      }
    } catch (error) {
      console.error('[Temu Detector] 获取原始 API 数据失败:', error)
      throw error
    }
  }

  // 获取 Temu 数据（商品列表、待办事项等）
  public async getTemuData(apiType: string, params: any, mallId: string): Promise<any> {
    try {
      console.info('[Temu Detector] 开始获取 Temu 数据...', { apiType, params, mallId })

      // 获取 anti-content 头部
      const antiContent = this.getAntiContentHeader()

      // 构建请求头
      const headers: Record<string, string> = {
        'accept': '*/*',
        'accept-language': 'zh-CN,zh;q=0.9',
        'content-type': 'application/json',
        'cache-control': 'max-age=0',
        'origin': 'https://seller.kuajingmaihuo.com',
        'referer': 'https://seller.kuajingmaihuo.com/main/product/seller-select',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
      }

      // 添加 anti-content 和 mallid
      if (antiContent) {
        headers['anti-content'] = antiContent
      }
      if (mallId) {
        headers['mallid'] = mallId
      }

      let url: string
      let body: string

      if (apiType === 'todo') {
        // 获取待办事项数量
        url = 'https://seller.kuajingmaihuo.com/marvel-supplier/api/xmen/select/queryTodoCount'
        body = JSON.stringify({})
      } else if (apiType === 'products') {
        // 获取商品列表
        url = 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/xmen/select/searchForSemiSupplier'
        const defaultParams = {
          pageSize: 50,
          pageNum: 1,
          supplierTodoTypeList: []
        }
        body = JSON.stringify({ ...defaultParams, ...params })
      } else {
        throw new Error(`不支持的 API 类型: ${apiType}`)
      }

      console.info('[Temu Detector] 发送请求:', { url, headers, body })

      const response = await fetch(url, {
        method: 'POST',
        headers,
        credentials: 'include',
        body
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      console.info('[Temu Detector] API 返回数据:', data)

      return data
    } catch (error) {
      console.error('[Temu Detector] 获取 Temu 数据失败:', error)
      throw error
    }
  }
}

// 检查是否在 Temu 商家后台页面
const isTemuSellerPage = (): boolean => {
  const hostname = window.location.hostname
  const temuDomains = [
    'seller.temu.com',
    'seller.kuajingmaihuo.com',
    'seller-cn.temu.com',
    'agentseller.temu.com',
    'agentseller-us.temu.com'
  ]

  const isDomain = temuDomains.some(domain => hostname.includes(domain))
  const isPath = window.location.href.includes('/seller') ||
                 window.location.href.includes('/agentseller')

  console.info('[Temu Detector] 页面检查 - 域名匹配:', isDomain, '路径匹配:', isPath)
  return isDomain || isPath
}

// 只在 Temu 商家后台页面运行
if (isTemuSellerPage()) {
  console.info('[Temu Detector] 在 Temu 商家后台页面，启动检测器...')
  const detector = new TemuShopDetector()

  // 将检测器暴露到全局，供扩展程序调用
  ;(window as unknown as { temuShopDetector: TemuShopDetector }).temuShopDetector = detector

  // 监听来自 side panel 的消息
  chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
    console.info('[Temu Detector] 收到消息:', request)

    if (request.action === 'GET_TEMU_INFO') {
      // 调用检测器获取原始 API 数据
      detector.getRawApiData()
        .then(result => {
          console.info('[Temu Detector] 获取原始 API 数据成功:', result)
          sendResponse({
            success: true,
            data: result
          })
        })
        .catch(error => {
          console.error('[Temu Detector] 获取原始 API 数据失败:', error)
          sendResponse({
            success: false,
            error: error.message || '获取失败'
          })
        })

      // 返回 true 表示异步响应
      return true
    }

    if (request.action === 'GET_ANTI_CONTENT') {
      // 获取 anti-content 头部
      const antiContent = detector.getAntiContentHeader()
      sendResponse({
        success: true,
        antiContent: antiContent
      })
      return true
    }

    if (request.action === 'GET_TEMU_DATA') {
      // 获取 Temu 数据（商品列表、待办事项等）
      detector.getTemuData(request.apiType, request.params, request.mallId)
        .then(result => {
          console.info('[Temu Detector] 获取 Temu 数据成功:', result)
          sendResponse({
            success: true,
            data: result
          })
        })
        .catch(error => {
          console.error('[Temu Detector] 获取 Temu 数据失败:', error)
          sendResponse({
            success: false,
            error: error.message || '获取失败'
          })
        })

      // 返回 true 表示异步响应
      return true
    }

    // 其他消息类型
    return false
  })
} else {
  console.info('[Temu Detector] 不在 Temu 商家后台页面，跳过检测')
}

// 导出 onExecute 函数供 loader 调用
export function onExecute(context?: { perf?: { injectTime: number; loadTime: number } }) {
  console.info('[Temu Detector] onExecute 被调用，性能信息:', context?.perf)
  console.info('[Temu Detector] 当前页面 URL:', window.location.href)
  console.info('[Temu Detector] 当前页面 hostname:', window.location.hostname)

  // 添加一个明显的页面标记，方便调试
  const debugElement = document.createElement('div')
  debugElement.id = 'temu-detector-debug'
  debugElement.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    background: #ff0000;
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    z-index: 999999;
    font-size: 12px;
    font-family: monospace;
  `
  debugElement.textContent = 'Temu Detector Loaded'
  document.body?.appendChild(debugElement)

  // 5秒后移除调试元素
  setTimeout(() => {
    debugElement.remove()
  }, 5000)
}

export { TemuShopDetector }