import amazonDataService from '../services/amazonDataService'
import configStorageService from '../services/configStorageService'

// 采集状态管理
let isCollecting = false
let collectedCount = 0

// 创建浮动采集按钮
function createFloatingButton(): HTMLElement {
  const button = document.createElement('button')
  button.className = 'ant-float-btn ant-float-btn-primary ant-float-btn-square'
  button.type = 'button'
  button.style.cssText = `
    position: fixed;
    right: 54px;
    bottom: 35vh;
    z-index: 9999;
    width: 56px;
    height: 56px;
    border-radius: 8px;
    border: none;
    background: #1677ff;
    color: white;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.3s ease;
  `

  // 创建图标
  const icon = document.createElement('div')
  icon.innerHTML = `
    <svg focusable="false" data-icon="rocket" width="1em" height="1em" fill="currentColor" aria-hidden="true" viewBox="64 64 896 896">
      <path d="M864 736c0-111.6-65.4-208-160-252.9V317.3c0-15.1-5.3-29.7-15.1-41.2L536.5 95.4C530.1 87.8 521 84 512 84s-18.1 3.8-24.5 11.4L335.1 276.1a63.97 63.97 0 00-15.1 41.2v165.8C225.4 528 160 624.4 160 736h156.5c-2.3 7.2-3.5 15-3.5 23.8 0 22.1 7.6 43.7 21.4 60.8a97.2 97.2 0 0043.1 30.6c23.1 54 75.6 88.8 134.5 88.8 29.1 0 57.3-8.6 81.4-24.8 23.6-15.8 41.9-37.9 53-64a97 97 0 0043.1-30.5 97.52 97.52 0 0021.4-60.8c0-8.4-1.1-16.4-3.1-23.8H864zM762.3 621.4c9.4 14.6 17 30.3 22.5 46.6H700V558.7a211.6 211.6 0 0162.3 62.7zM388 483.1V318.8l124-147 124 147V668H388V483.1zM239.2 668c5.5-16.3 13.1-32 22.5-46.6 16.3-25.2 37.5-46.5 62.3-62.7V668h-84.8zm388.9 116.2c-5.2 3-11.2 4.2-17.1 3.4l-19.5-2.4-2.8 19.4c-5.4 37.9-38.4 66.5-76.7 66.5-38.3 0-71.3-28.6-76.7-66.5l-2.8-19.5-19.5 2.5a27.7 27.7 0 01-17.1-3.5c-8.7-5-14.1-14.3-14.1-24.4 0-10.6 5.9-19.4 14.6-23.8h231.3c8.8 4.5 14.6 13.3 14.6 23.8-.1 10.2-5.5 19.6-14.2 24.5zM464 400a48 48 0 1096 0 48 48 0 10-96 0z"></path>
    </svg>
  `
  button.appendChild(icon)

  // 创建文字描述
  const description = document.createElement('div')
  description.textContent = isAmazonSearchPage() ? '批量上品' : '单品采集'
  description.style.cssText = `
    font-size: 10px;
    margin-top: 2px;
    line-height: 1;
  `
  button.appendChild(description)

  // 创建计数徽章
  const badge = document.createElement('sup')
  badge.className = 'collection-badge'
  badge.style.cssText = `
    position: absolute;
    top: -8px;
    right: -8px;
    background: #ff4d4f;
    color: white;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 10px;
    min-width: 16px;
    text-align: center;
    display: ${collectedCount > 0 ? 'block' : 'none'};
  `
  badge.textContent = collectedCount.toString()
  button.appendChild(badge)

  // 添加点击事件
  button.addEventListener('click', handleCollectionClick)

  // 添加悬停效果
  button.addEventListener('mouseenter', () => {
    button.style.transform = 'scale(1.1)'
    button.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.25)'
  })

  button.addEventListener('mouseleave', () => {
    button.style.transform = 'scale(1)'
    button.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)'
  })

  return button
}

// 创建单品采集按钮
function createProductCollectionButton(productElement: Element): HTMLElement {
  const button = document.createElement('span')
  button.className = 'amazon-product-collect-btn'
  button.style.cssText = `
    display: inline-block;
    background: #1677ff;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    margin-left: 8px;
    transition: all 0.3s ease;
    user-select: none;
  `
  button.textContent = '采集'

  // 添加悬停效果
  button.addEventListener('mouseenter', () => {
    button.style.background = '#4096ff'
    button.style.transform = 'scale(1.05)'
  })

  button.addEventListener('mouseleave', () => {
    button.style.background = '#1677ff'
    button.style.transform = 'scale(1)'
  })

  // 添加点击事件
  button.addEventListener('click', (e) => {
    e.preventDefault()
    e.stopPropagation()
    handleSingleProductCollection(productElement)
  })

  return button
}

// 处理采集按钮点击
async function handleCollectionClick() {
  if (isCollecting) {
    showNotification('正在采集中，请稍候...', 'warning')
    return
  }

  try {
    // 检查配置完整性
    const configCheck = await configStorageService.isConfigComplete()
    if (!configCheck.isComplete) {
      showNotification(`配置不完整，缺少：${configCheck.missingFields.join(', ')}`, 'error')
      return
    }

    if (isAmazonSearchPage()) {
      await handleBatchCollection()
    } else if (isAmazonProductPage()) {
      await handleSingleProductCollection()
    } else {
      showNotification('当前页面不支持采集', 'warning')
    }
  } catch (error) {
    console.error('[AmazonCollector] 采集失败:', error)
    showNotification('采集失败: ' + (error instanceof Error ? error.message : '未知错误'), 'error')
  }
}

// 处理单品采集
async function handleSingleProductCollection(productElement?: Element) {
  try {
    isCollecting = true
    updateButtonState()
    
    showNotification('开始采集商品数据...', 'info')

    // 提取Amazon产品数据
    const amazonData = await amazonDataService.extractProductDataFromPage()
    if (!amazonData) {
      throw new Error('无法提取产品数据')
    }

    // 转换为店小秘格式
    const dianxiaomiData = await amazonDataService.convertToDianxiaomiFormat(amazonData)

    // 模拟推送到店小秘
    await amazonDataService.simulatePushToDianxiaomi(dianxiaomiData)

    collectedCount++
    updateBadgeCount()
    
    showNotification(`商品 "${amazonData.title.substring(0, 30)}..." 采集成功！`, 'success')

    // 如果是从产品列表中采集，标记该产品已采集
    if (productElement) {
      markProductAsCollected(productElement)
    }

  } catch (error) {
    console.error('[AmazonCollector] 单品采集失败:', error)
    showNotification('单品采集失败: ' + (error instanceof Error ? error.message : '未知错误'), 'error')
  } finally {
    isCollecting = false
    updateButtonState()
  }
}

// 处理批量采集
async function handleBatchCollection() {
  try {
    isCollecting = true
    updateButtonState()

    showNotification('开始批量采集...', 'info')

    // 获取当前页面的所有产品
    const productElements = document.querySelectorAll('[data-asin]:not(.amazon-collected)')
    
    if (productElements.length === 0) {
      showNotification('当前页面没有找到可采集的商品', 'warning')
      return
    }

    let successCount = 0
    let failCount = 0

    for (let i = 0; i < productElements.length; i++) {
      const productElement = productElements[i]
      const asin = productElement.getAttribute('data-asin')
      
      if (!asin) continue

      try {
        showNotification(`正在采集第 ${i + 1}/${productElements.length} 个商品...`, 'info')
        
        // 模拟点击产品链接获取详情
        const productLink = productElement.querySelector('h3 a, .s-link-style a') as HTMLAnchorElement
        if (productLink) {
          // 这里可以实现在新标签页中打开产品页面进行采集
          // 暂时跳过，直接标记为已采集
          markProductAsCollected(productElement)
          successCount++
          collectedCount++
        }

        // 添加延迟避免请求过快
        await new Promise(resolve => setTimeout(resolve, 1000))

      } catch (error) {
        console.error(`[AmazonCollector] 采集商品 ${asin} 失败:`, error)
        failCount++
      }
    }

    updateBadgeCount()
    showNotification(`批量采集完成！成功: ${successCount}, 失败: ${failCount}`, 'success')

    // 检查是否有下一页
    if (hasNextPage()) {
      const shouldContinue = confirm('当前页面采集完成，是否继续采集下一页？')
      if (shouldContinue) {
        goToNextPage()
      }
    }

  } catch (error) {
    console.error('[AmazonCollector] 批量采集失败:', error)
    showNotification('批量采集失败: ' + (error instanceof Error ? error.message : '未知错误'), 'error')
  } finally {
    isCollecting = false
    updateButtonState()
  }
}

// 标记产品为已采集
function markProductAsCollected(productElement: Element) {
  productElement.classList.add('amazon-collected')
  productElement.setAttribute('style', 'opacity: 0.6; border: 2px solid #52c41a;')
  
  // 添加已采集标记
  const badge = document.createElement('div')
  badge.style.cssText = `
    position: absolute;
    top: 8px;
    right: 8px;
    background: #52c41a;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    z-index: 10;
  `
  badge.textContent = '已采集'
  
  const container = productElement.querySelector('[data-cy="title-recipe-container"], .s-result-item')
  if (container) {
    container.style.position = 'relative'
    container.appendChild(badge)
  }
}

// 更新按钮状态
function updateButtonState() {
  const button = document.querySelector('.ant-float-btn') as HTMLElement
  if (button) {
    if (isCollecting) {
      button.style.background = '#faad14'
      button.style.cursor = 'not-allowed'
      const description = button.querySelector('div:last-child') as HTMLElement
      if (description) {
        description.textContent = '采集中...'
      }
    } else {
      button.style.background = '#1677ff'
      button.style.cursor = 'pointer'
      const description = button.querySelector('div:last-child') as HTMLElement
      if (description) {
        description.textContent = isAmazonSearchPage() ? '批量上品' : '单品采集'
      }
    }
  }
}

// 更新徽章计数
function updateBadgeCount() {
  const badge = document.querySelector('.collection-badge') as HTMLElement
  if (badge) {
    badge.textContent = collectedCount.toString()
    badge.style.display = collectedCount > 0 ? 'block' : 'none'
  }
}

// 显示通知
function showNotification(message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') {
  // 创建通知元素
  const notification = document.createElement('div')
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    padding: 12px 16px;
    border-radius: 6px;
    color: white;
    font-size: 14px;
    max-width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    transform: translateX(100%);
  `

  // 设置颜色
  const colors = {
    success: '#52c41a',
    error: '#ff4d4f',
    warning: '#faad14',
    info: '#1677ff'
  }
  notification.style.background = colors[type]
  notification.textContent = message

  document.body.appendChild(notification)

  // 动画显示
  setTimeout(() => {
    notification.style.transform = 'translateX(0)'
  }, 100)

  // 自动隐藏
  setTimeout(() => {
    notification.style.transform = 'translateX(100%)'
    setTimeout(() => {
      document.body.removeChild(notification)
    }, 300)
  }, 3000)
}

// 工具函数
function isAmazonSearchPage(): boolean {
  return window.location.href.includes('amazon.com/s') || 
         window.location.href.includes('amazon.com/gp/search')
}

function isAmazonProductPage(): boolean {
  return window.location.href.includes('amazon.com') && 
         (window.location.href.includes('/dp/') || window.location.href.includes('/gp/product/'))
}

function hasNextPage(): boolean {
  const nextPageLink = document.querySelector('a[aria-label*="next page"], a[aria-label*="Next page"]')
  return nextPageLink !== null
}

function goToNextPage() {
  const nextPageLink = document.querySelector('a[aria-label*="next page"], a[aria-label*="Next page"]') as HTMLAnchorElement
  if (nextPageLink) {
    window.location.href = nextPageLink.href
  }
}

// 初始化
function init() {
  // 检查是否为Amazon页面
  if (!window.location.href.includes('amazon.com')) {
    return
  }

  console.info('[AmazonCollector] 初始化Amazon采集器...')

  // 等待页面加载完成
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init)
    return
  }

  // 创建浮动按钮
  const floatingButton = createFloatingButton()
  document.body.appendChild(floatingButton)

  // 如果是搜索页面，为每个产品添加采集按钮
  if (isAmazonSearchPage()) {
    addProductCollectionButtons()
  }

  console.info('[AmazonCollector] Amazon采集器初始化完成')
}

// 为产品添加采集按钮
function addProductCollectionButtons() {
  const productElements = document.querySelectorAll('[data-asin]')
  
  productElements.forEach(productElement => {
    // 避免重复添加
    if (productElement.querySelector('.amazon-product-collect-btn')) {
      return
    }

    const titleElement = productElement.querySelector('h3 a, .s-link-style a')
    if (titleElement) {
      const collectButton = createProductCollectionButton(productElement)
      titleElement.parentNode?.appendChild(collectButton)
    }
  })
}

// 启动
init()

// 监听页面变化（SPA路由）
let lastUrl = location.href
new MutationObserver(() => {
  const url = location.href
  if (url !== lastUrl) {
    lastUrl = url
    setTimeout(init, 1000) // 延迟重新初始化
  }
}).observe(document, { subtree: true, childList: true })
