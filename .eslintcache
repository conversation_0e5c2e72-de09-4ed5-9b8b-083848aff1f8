[{"/projects/vite-vue3-chrome-extension-v3/define.config.mjs": "1", "/projects/vite-vue3-chrome-extension-v3/manifest.chrome.config.ts": "2", "/projects/vite-vue3-chrome-extension-v3/manifest.config.ts": "3", "/projects/vite-vue3-chrome-extension-v3/manifest.firefox.config.ts": "4", "/projects/vite-vue3-chrome-extension-v3/postcss.config.cjs": "5", "/projects/vite-vue3-chrome-extension-v3/scripts/getInstalledBrowsers.ts": "6", "/projects/vite-vue3-chrome-extension-v3/scripts/launch.ts": "7", "/projects/vite-vue3-chrome-extension-v3/src/background/index.ts": "8", "/projects/vite-vue3-chrome-extension-v3/src/components/AppFooter.vue": "9", "/projects/vite-vue3-chrome-extension-v3/src/components/AppHeader.vue": "10", "/projects/vite-vue3-chrome-extension-v3/src/components/RouterLinkUp.vue": "11", "/projects/vite-vue3-chrome-extension-v3/src/components/TestComponent.vue": "12", "/projects/vite-vue3-chrome-extension-v3/src/components/ThemeSwitch.vue": "13", "/projects/vite-vue3-chrome-extension-v3/src/components/state/DisplayError.vue": "14", "/projects/vite-vue3-chrome-extension-v3/src/components/state/LoadingSpinner.vue": "15", "/projects/vite-vue3-chrome-extension-v3/src/components/state/tailwind-empty-state.vue": "16", "/projects/vite-vue3-chrome-extension-v3/src/composables/useBrowserStorage.ts": "17", "/projects/vite-vue3-chrome-extension-v3/src/composables/useTheme.ts": "18", "/projects/vite-vue3-chrome-extension-v3/src/content-script/index.ts": "19", "/projects/vite-vue3-chrome-extension-v3/src/devtools/index.ts": "20", "/projects/vite-vue3-chrome-extension-v3/src/offscreen/index.ts": "21", "/projects/vite-vue3-chrome-extension-v3/src/stores/options.store.ts": "22", "/projects/vite-vue3-chrome-extension-v3/src/stores/test.store.ts": "23", "/projects/vite-vue3-chrome-extension-v3/src/ui/action-popup/app.vue": "24", "/projects/vite-vue3-chrome-extension-v3/src/ui/action-popup/index.ts": "25", "/projects/vite-vue3-chrome-extension-v3/src/ui/action-popup/pages/index.vue": "26", "/projects/vite-vue3-chrome-extension-v3/src/ui/action-popup/pages/playground.vue": "27", "/projects/vite-vue3-chrome-extension-v3/src/ui/common/pages/404.vue": "28", "/projects/vite-vue3-chrome-extension-v3/src/ui/common/pages/about.vue": "29", "/projects/vite-vue3-chrome-extension-v3/src/ui/common/pages/change-log.vue": "30", "/projects/vite-vue3-chrome-extension-v3/src/ui/common/pages/features.vue": "31", "/projects/vite-vue3-chrome-extension-v3/src/ui/common/pages/help.vue": "32", "/projects/vite-vue3-chrome-extension-v3/src/ui/common/pages/privacy-policy.vue": "33", "/projects/vite-vue3-chrome-extension-v3/src/ui/common/pages/terms-of-service.vue": "34", "/projects/vite-vue3-chrome-extension-v3/src/ui/content-script-iframe/app.vue": "35", "/projects/vite-vue3-chrome-extension-v3/src/ui/content-script-iframe/index.ts": "36", "/projects/vite-vue3-chrome-extension-v3/src/ui/content-script-iframe/pages/index.vue": "37", "/projects/vite-vue3-chrome-extension-v3/src/ui/devtools-panel/app.vue": "38", "/projects/vite-vue3-chrome-extension-v3/src/ui/devtools-panel/index.ts": "39", "/projects/vite-vue3-chrome-extension-v3/src/ui/devtools-panel/pages/index.vue": "40", "/projects/vite-vue3-chrome-extension-v3/src/ui/options-page/app.vue": "41", "/projects/vite-vue3-chrome-extension-v3/src/ui/options-page/index.ts": "42", "/projects/vite-vue3-chrome-extension-v3/src/ui/options-page/pages/index.vue": "43", "/projects/vite-vue3-chrome-extension-v3/src/ui/setup/app.vue": "44", "/projects/vite-vue3-chrome-extension-v3/src/ui/setup/index.ts": "45", "/projects/vite-vue3-chrome-extension-v3/src/ui/setup/pages/install.vue": "46", "/projects/vite-vue3-chrome-extension-v3/src/ui/setup/pages/update.vue": "47", "/projects/vite-vue3-chrome-extension-v3/src/ui/side-panel/app.vue": "48", "/projects/vite-vue3-chrome-extension-v3/src/ui/side-panel/index.ts": "49", "/projects/vite-vue3-chrome-extension-v3/src/ui/side-panel/pages/index.vue": "50", "/projects/vite-vue3-chrome-extension-v3/src/utils/i18n.ts": "51", "/projects/vite-vue3-chrome-extension-v3/src/utils/pinia.ts": "52", "/projects/vite-vue3-chrome-extension-v3/src/utils/router/index.ts": "53", "/projects/vite-vue3-chrome-extension-v3/vite.chrome.config.ts": "54", "/projects/vite-vue3-chrome-extension-v3/vite.config.ts": "55", "/projects/vite-vue3-chrome-extension-v3/vite.firefox.config.ts": "56"}, {"size": 1103, "mtime": 1746041243393, "results": "57", "hashOfConfig": "58"}, {"size": 263, "mtime": 1745512022861, "results": "59", "hashOfConfig": "60"}, {"size": 1769, "mtime": 1745512022861, "results": "61", "hashOfConfig": "60"}, {"size": 624, "mtime": 1745512022861, "results": "62", "hashOfConfig": "60"}, {"size": 71, "mtime": 1742119274149, "results": "63", "hashOfConfig": "64"}, {"size": 8989, "mtime": 1745512022879, "results": "65", "hashOfConfig": "66"}, {"size": 3127, "mtime": 1745512022880, "results": "67", "hashOfConfig": "66"}, {"size": 1152, "mtime": 1745512022881, "results": "68", "hashOfConfig": "60"}, {"size": 1935, "mtime": 1745512022881, "results": "69", "hashOfConfig": "70"}, {"size": 728, "mtime": 1745522673795, "results": "71", "hashOfConfig": "70"}, {"size": 273, "mtime": 1745519975186, "results": "72", "hashOfConfig": "70"}, {"size": 849, "mtime": 1745519214590, "results": "73", "hashOfConfig": "70"}, {"size": 249, "mtime": 1745521995412, "results": "74", "hashOfConfig": "70"}, {"size": 258, "mtime": 1745512022882, "results": "75", "hashOfConfig": "70"}, {"size": 239, "mtime": 1745512022883, "results": "76", "hashOfConfig": "70"}, {"size": 101532, "mtime": 1745512022885, "results": "77", "hashOfConfig": "70"}, {"size": 3073, "mtime": 1745512022885, "results": "78", "hashOfConfig": "60"}, {"size": 1237, "mtime": 1745521974873, "results": "79", "hashOfConfig": "60"}, {"size": 737, "mtime": 1745522473085, "results": "80", "hashOfConfig": "60"}, {"size": 737, "mtime": 1745512022886, "results": "81", "hashOfConfig": "60"}, {"size": 201, "mtime": 1745512022887, "results": "82", "hashOfConfig": "60"}, {"size": 471, "mtime": 1745512022887, "results": "83", "hashOfConfig": "60"}, {"size": 560, "mtime": 1745512022887, "results": "84", "hashOfConfig": "60"}, {"size": 217, "mtime": 1745520210369, "results": "85", "hashOfConfig": "70"}, {"size": 804, "mtime": 1745515451097, "results": "86", "hashOfConfig": "60"}, {"size": 1362, "mtime": 1745520128662, "results": "87", "hashOfConfig": "88"}, {"size": 127, "mtime": 1745512022890, "results": "89", "hashOfConfig": "88"}, {"size": 371, "mtime": 1745520244287, "results": "90", "hashOfConfig": "88"}, {"size": 664, "mtime": 1745520271436, "results": "91", "hashOfConfig": "88"}, {"size": 528, "mtime": 1745515789740, "results": "92", "hashOfConfig": "88"}, {"size": 5772, "mtime": 1745520658670, "results": "93", "hashOfConfig": "88"}, {"size": 747, "mtime": 1745520263310, "results": "94", "hashOfConfig": "88"}, {"size": 1644, "mtime": 1745512022891, "results": "95", "hashOfConfig": "88"}, {"size": 1772, "mtime": 1745512022891, "results": "96", "hashOfConfig": "88"}, {"size": 217, "mtime": 1745520195032, "results": "97", "hashOfConfig": "70"}, {"size": 679, "mtime": 1745515450789, "results": "98", "hashOfConfig": "60"}, {"size": 139, "mtime": 1745522467953, "results": "99", "hashOfConfig": "88"}, {"size": 217, "mtime": 1745520195387, "results": "100", "hashOfConfig": "70"}, {"size": 672, "mtime": 1745515451305, "results": "101", "hashOfConfig": "60"}, {"size": 136, "mtime": 1745512022893, "results": "102", "hashOfConfig": "88"}, {"size": 217, "mtime": 1745520194935, "results": "103", "hashOfConfig": "70"}, {"size": 670, "mtime": 1745515450826, "results": "104", "hashOfConfig": "60"}, {"size": 1655, "mtime": 1745522395114, "results": "105", "hashOfConfig": "88"}, {"size": 228, "mtime": 1745520194935, "results": "106", "hashOfConfig": "70"}, {"size": 690, "mtime": 1745515450770, "results": "107", "hashOfConfig": "60"}, {"size": 456, "mtime": 1745515488099, "results": "108", "hashOfConfig": "88"}, {"size": 849, "mtime": 1745512022894, "results": "109", "hashOfConfig": "88"}, {"size": 217, "mtime": 1745520195299, "results": "110", "hashOfConfig": "70"}, {"size": 668, "mtime": 1745515451183, "results": "111", "hashOfConfig": "60"}, {"size": 132, "mtime": 1745512022894, "results": "112", "hashOfConfig": "88"}, {"size": 375, "mtime": 1745512022894, "results": "113", "hashOfConfig": "60"}, {"size": 72, "mtime": 1745512022894, "results": "114", "hashOfConfig": "60"}, {"size": 357, "mtime": 1745512022894, "results": "115", "hashOfConfig": "60"}, {"size": 2527, "mtime": 1745512022894, "results": "116", "hashOfConfig": "60"}, {"size": 4468, "mtime": 1746041429548, "results": "117", "hashOfConfig": "60"}, {"size": 2529, "mtime": 1745512022894, "results": "118", "hashOfConfig": "60"}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "cpouo", {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10gdb2w", {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "usghzr", {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1vdoete", {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1mfawwl", {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "a49ii2", {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/projects/vite-vue3-chrome-extension-v3/define.config.mjs", [], [], "/projects/vite-vue3-chrome-extension-v3/manifest.chrome.config.ts", [], [], "/projects/vite-vue3-chrome-extension-v3/manifest.config.ts", [], [], "/projects/vite-vue3-chrome-extension-v3/manifest.firefox.config.ts", [], [], "/projects/vite-vue3-chrome-extension-v3/postcss.config.cjs", [], [], "/projects/vite-vue3-chrome-extension-v3/scripts/getInstalledBrowsers.ts", [], ["287", "288"], "/projects/vite-vue3-chrome-extension-v3/scripts/launch.ts", [], [], "/projects/vite-vue3-chrome-extension-v3/src/background/index.ts", [], [], "/projects/vite-vue3-chrome-extension-v3/src/components/AppFooter.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/components/AppHeader.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/components/RouterLinkUp.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/components/TestComponent.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/components/ThemeSwitch.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/components/state/DisplayError.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/components/state/LoadingSpinner.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/components/state/tailwind-empty-state.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/composables/useBrowserStorage.ts", ["289", "290", "291", "292", "293", "294", "295"], [], "/projects/vite-vue3-chrome-extension-v3/src/composables/useTheme.ts", [], [], "/projects/vite-vue3-chrome-extension-v3/src/content-script/index.ts", [], [], "/projects/vite-vue3-chrome-extension-v3/src/devtools/index.ts", [], [], "/projects/vite-vue3-chrome-extension-v3/src/offscreen/index.ts", [], [], "/projects/vite-vue3-chrome-extension-v3/src/stores/options.store.ts", [], [], "/projects/vite-vue3-chrome-extension-v3/src/stores/test.store.ts", [], [], "/projects/vite-vue3-chrome-extension-v3/src/ui/action-popup/app.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/ui/action-popup/index.ts", [], [], "/projects/vite-vue3-chrome-extension-v3/src/ui/action-popup/pages/index.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/ui/action-popup/pages/playground.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/ui/common/pages/404.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/ui/common/pages/about.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/ui/common/pages/change-log.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/ui/common/pages/features.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/ui/common/pages/help.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/ui/common/pages/privacy-policy.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/ui/common/pages/terms-of-service.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/ui/content-script-iframe/app.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/ui/content-script-iframe/index.ts", [], [], "/projects/vite-vue3-chrome-extension-v3/src/ui/content-script-iframe/pages/index.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/ui/devtools-panel/app.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/ui/devtools-panel/index.ts", [], [], "/projects/vite-vue3-chrome-extension-v3/src/ui/devtools-panel/pages/index.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/ui/options-page/app.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/ui/options-page/index.ts", [], [], "/projects/vite-vue3-chrome-extension-v3/src/ui/options-page/pages/index.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/ui/setup/app.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/ui/setup/index.ts", [], [], "/projects/vite-vue3-chrome-extension-v3/src/ui/setup/pages/install.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/ui/setup/pages/update.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/ui/side-panel/app.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/ui/side-panel/index.ts", [], [], "/projects/vite-vue3-chrome-extension-v3/src/ui/side-panel/pages/index.vue", [], [], "/projects/vite-vue3-chrome-extension-v3/src/utils/i18n.ts", [], [], "/projects/vite-vue3-chrome-extension-v3/src/utils/pinia.ts", [], [], "/projects/vite-vue3-chrome-extension-v3/src/utils/router/index.ts", [], [], "/projects/vite-vue3-chrome-extension-v3/vite.chrome.config.ts", [], [], "/projects/vite-vue3-chrome-extension-v3/vite.config.ts", [], [], "/projects/vite-vue3-chrome-extension-v3/vite.firefox.config.ts", [], [], {"ruleId": "296", "severity": 2, "message": "297", "line": 77, "column": 13, "nodeType": "298", "messageId": "299", "endLine": 77, "endColumn": 15, "suggestions": "300", "suppressions": "301"}, {"ruleId": "296", "severity": 2, "message": "297", "line": 83, "column": 13, "nodeType": "298", "messageId": "299", "endLine": 83, "endColumn": 15, "suggestions": "302", "suppressions": "303"}, {"ruleId": "304", "severity": 1, "message": "305", "line": 3, "column": 30, "nodeType": "306", "messageId": "307", "endLine": 3, "endColumn": 33, "suggestions": "308"}, {"ruleId": "304", "severity": 1, "message": "305", "line": 3, "column": 43, "nodeType": "306", "messageId": "307", "endLine": 3, "endColumn": 46, "suggestions": "309"}, {"ruleId": "304", "severity": 1, "message": "305", "line": 3, "column": 49, "nodeType": "306", "messageId": "307", "endLine": 3, "endColumn": 52, "suggestions": "310"}, {"ruleId": "311", "severity": 1, "message": "312", "line": 19, "column": 7, "nodeType": "313", "messageId": "314", "endLine": 19, "endColumn": 18, "suggestions": "315"}, {"ruleId": "304", "severity": 1, "message": "305", "line": 26, "column": 34, "nodeType": "306", "messageId": "307", "endLine": 26, "endColumn": 37, "suggestions": "316"}, {"ruleId": "304", "severity": 1, "message": "305", "line": 26, "column": 46, "nodeType": "306", "messageId": "307", "endLine": 26, "endColumn": 49, "suggestions": "317"}, {"ruleId": "304", "severity": 1, "message": "305", "line": 36, "column": 26, "nodeType": "306", "messageId": "307", "endLine": 36, "endColumn": 29, "suggestions": "318"}, "no-empty", "Empty block statement.", "BlockStatement", "unexpected", ["319"], ["320"], ["321"], ["322"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["323", "324"], ["325", "326"], ["327", "328"], "no-console", "Unexpected console statement. Only these console methods are allowed: info, warn, error.", "MemberExpression", "limited", ["329"], ["330", "331"], ["332", "333"], ["334", "335"], {"messageId": "336", "data": "337", "fix": "338", "desc": "339"}, {"kind": "340", "justification": "341"}, {"messageId": "336", "data": "342", "fix": "343", "desc": "339"}, {"kind": "340", "justification": "341"}, {"messageId": "344", "fix": "345", "desc": "346"}, {"messageId": "347", "fix": "348", "desc": "349"}, {"messageId": "344", "fix": "350", "desc": "346"}, {"messageId": "347", "fix": "351", "desc": "349"}, {"messageId": "344", "fix": "352", "desc": "346"}, {"messageId": "347", "fix": "353", "desc": "349"}, {"fix": "354", "messageId": "355", "data": "356", "desc": "357"}, {"messageId": "344", "fix": "358", "desc": "346"}, {"messageId": "347", "fix": "359", "desc": "349"}, {"messageId": "344", "fix": "360", "desc": "346"}, {"messageId": "347", "fix": "361", "desc": "349"}, {"messageId": "344", "fix": "362", "desc": "346"}, {"messageId": "347", "fix": "363", "desc": "349"}, "suggestComment", {"type": "364"}, {"range": "365", "text": "366"}, "Add comment inside empty block statement.", "directive", "", {"type": "364"}, {"range": "367", "text": "366"}, "suggestUnknown", {"range": "368", "text": "369"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "370", "text": "371"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "372", "text": "369"}, {"range": "373", "text": "371"}, {"range": "374", "text": "369"}, {"range": "375", "text": "371"}, {"range": "376", "text": "341"}, "removeConsole", {"propertyName": "377"}, "Remove the console.log().", {"range": "378", "text": "369"}, {"range": "379", "text": "371"}, {"range": "380", "text": "369"}, {"range": "381", "text": "371"}, {"range": "382", "text": "369"}, {"range": "383", "text": "371"}, "block", [2014, 2014], " /* empty */ ", [2157, 2157], [73, 76], "unknown", [73, 76], "never", [86, 89], [86, 89], [92, 95], [92, 95], [683, 729], "log", [794, 797], [794, 797], [806, 809], [806, 809], [1136, 1139], [1136, 1139]]