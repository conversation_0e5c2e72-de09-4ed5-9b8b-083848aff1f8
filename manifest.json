{
  // ...existing manifest properties...
  "content_security_policy": {
    "extension_pages": "script-src 'self' 'wasm-unsafe-eval' 'inline-speculation-rules' http://localhost:* http://127.0.0.1:* 'unsafe-inline'; object-src 'self'; connect-src 'self' http://localhost:* http://127.0.0.1:* ws://localhost:* ws://127.0.0.1:*;"
    // If you have other specific directives, ensure they are preserved.
    // The key changes are adding 'unsafe-inline' to script-src
    // and ensuring connect-src allows http/ws connections to localhost and 127.0.0.1.
  }
  // ...existing manifest properties...
}
