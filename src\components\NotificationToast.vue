<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

interface NotificationProps {
  type?: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  showClose?: boolean
}

const props = withDefaults(defineProps<NotificationProps>(), {
  type: 'info',
  duration: 4000,
  showClose: true
})

const emit = defineEmits<{
  close: []
}>()

const visible = ref(true)
let timer: NodeJS.Timeout | null = null

const close = () => {
  visible.value = false
  emit('close')
}

const getTypeClasses = () => {
  const baseClasses = 'border-l-4 p-4 rounded-lg shadow-lg'
  switch (props.type) {
    case 'success':
      return `${baseClasses} bg-green-50 border-green-400 text-green-800`
    case 'error':
      return `${baseClasses} bg-red-50 border-red-400 text-red-800`
    case 'warning':
      return `${baseClasses} bg-yellow-50 border-yellow-400 text-yellow-800`
    default:
      return `${baseClasses} bg-blue-50 border-blue-400 text-blue-800`
  }
}

const getIconClasses = () => {
  switch (props.type) {
    case 'success':
      return 'text-green-400'
    case 'error':
      return 'text-red-400'
    case 'warning':
      return 'text-yellow-400'
    default:
      return 'text-blue-400'
  }
}

const getIcon = () => {
  switch (props.type) {
    case 'success':
      return '✓'
    case 'error':
      return '✕'
    case 'warning':
      return '⚠'
    default:
      return 'ℹ'
  }
}

onMounted(() => {
  if (props.duration > 0) {
    timer = setTimeout(() => {
      close()
    }, props.duration)
  }
})

onUnmounted(() => {
  if (timer) {
    clearTimeout(timer)
  }
})
</script>

<template>
  <Transition
    enter-active-class="transition ease-out duration-300"
    enter-from-class="opacity-0 transform translate-y-2"
    enter-to-class="opacity-100 transform translate-y-0"
    leave-active-class="transition ease-in duration-200"
    leave-from-class="opacity-100 transform translate-y-0"
    leave-to-class="opacity-0 transform translate-y-2"
  >
    <div v-if="visible" :class="getTypeClasses()" class="max-w-sm w-full">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <span :class="getIconClasses()" class="text-xl font-bold">
            {{ getIcon() }}
          </span>
        </div>
        <div class="ml-3 flex-1">
          <h4 class="text-sm font-medium">{{ title }}</h4>
          <p v-if="message" class="mt-1 text-sm opacity-90">{{ message }}</p>
        </div>
        <div v-if="showClose" class="ml-4 flex-shrink-0">
          <button
            @click="close"
            class="inline-flex text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600 transition ease-in-out duration-150"
          >
            <span class="sr-only">关闭</span>
            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </Transition>
</template>
