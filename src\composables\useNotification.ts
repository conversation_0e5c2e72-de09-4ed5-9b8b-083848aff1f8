import { ref, createApp, h } from 'vue'
import NotificationToast from '../components/NotificationToast.vue'

interface NotificationOptions {
  type?: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  showClose?: boolean
}

const notifications = ref<Array<{ id: string; component: any }>>([])

let notificationContainer: HTMLElement | null = null

// 创建通知容器
const createNotificationContainer = () => {
  if (!notificationContainer) {
    notificationContainer = document.createElement('div')
    notificationContainer.id = 'notification-container'
    notificationContainer.className = 'fixed top-4 right-4 z-50 space-y-2'
    document.body.appendChild(notificationContainer)
  }
  return notificationContainer
}

// 显示通知
const showNotification = (options: NotificationOptions) => {
  const container = createNotificationContainer()
  const id = `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  
  // 创建通知元素
  const notificationElement = document.createElement('div')
  notificationElement.id = id
  container.appendChild(notificationElement)
  
  // 创建 Vue 应用实例
  const app = createApp({
    render() {
      return h(NotificationToast, {
        ...options,
        onClose: () => {
          removeNotification(id)
        }
      })
    }
  })
  
  // 挂载到元素
  app.mount(notificationElement)
  
  // 记录通知
  notifications.value.push({ id, component: app })
  
  return id
}

// 移除通知
const removeNotification = (id: string) => {
  const element = document.getElementById(id)
  if (element) {
    element.remove()
  }
  
  const index = notifications.value.findIndex(n => n.id === id)
  if (index > -1) {
    notifications.value[index].component.unmount()
    notifications.value.splice(index, 1)
  }
}

// 清除所有通知
const clearAllNotifications = () => {
  notifications.value.forEach(notification => {
    removeNotification(notification.id)
  })
}

// 便捷方法
const success = (title: string, message?: string, duration = 4000) => {
  return showNotification({ type: 'success', title, message, duration })
}

const error = (title: string, message?: string, duration = 6000) => {
  return showNotification({ type: 'error', title, message, duration })
}

const warning = (title: string, message?: string, duration = 5000) => {
  return showNotification({ type: 'warning', title, message, duration })
}

const info = (title: string, message?: string, duration = 4000) => {
  return showNotification({ type: 'info', title, message, duration })
}

export function useNotification() {
  return {
    // 状态
    notifications: readonly(notifications),
    
    // 方法
    showNotification,
    removeNotification,
    clearAllNotifications,
    
    // 便捷方法
    success,
    error,
    warning,
    info
  }
}
