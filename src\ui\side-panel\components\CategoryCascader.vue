<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { DownOutlined } from '@ant-design/icons-vue'
import type { ProductCategory } from '../../../services/dianxiaomiDetectionService'

// Props
interface Props {
  value?: number[]
  placeholder?: string
  shopId?: string
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择商品分类',
  shopId: ''
})

// Emits
const emit = defineEmits<{
  'update:value': [value: number[]]
  'change': [value: number[], selectedOptions: CascaderOption[]]
}>()

// 级联选择器选项类型
interface CascaderOption {
  value: number
  label: string
  isLeaf?: boolean
  loading?: boolean
  children?: CascaderOption[]
}

// 状态
const loading = ref(false)
const options = ref<CascaderOption[]>([])

// 计算属性
const selectedValue = computed({
  get: () => props.value || [],
  set: (value) => emit('update:value', value)
})

// 加载分类数据
const loadCategories = async (parentId?: number): Promise<ProductCategory[]> => {
  if (!props.shopId) {
    console.warn('[CategoryCascader] 缺少 shopId 参数')
    return []
  }

  try {
    console.info('[CategoryCascader] 加载分类数据，父ID:', parentId, '店铺ID:', props.shopId)
    
    // 向content script发送消息，请求加载分类数据
    const dianxiaomiTab = await chrome.tabs.query({ url: '*://www.dianxiaomi.com/*' })
    if (dianxiaomiTab.length === 0) {
      throw new Error('未找到店小秘标签页')
    }

    const requestData: any = {
      action: 'GET_TEMU_CATEGORIES',
      shopId: props.shopId,
      categoryParentId: parentId || 0  // 默认为 0，获取根分类
    }

    console.info('[CategoryCascader] 发送请求数据:', requestData)
    console.info('[CategoryCascader] 目标标签页:', dianxiaomiTab[0])

    const response = await chrome.tabs.sendMessage(dianxiaomiTab[0].id!, requestData)
    console.info('[CategoryCascader] API 响应:', response)

    if (response && response.success) {
      console.info('[CategoryCascader] 分类数据加载成功:', response.data)
      return response.data || []
    } else {
      throw new Error(response?.error || '加载分类失败')
    }
  } catch (error) {
    console.error('[CategoryCascader] 加载分类失败:', error)
    return []
  }
}

// 转换分类数据为级联选择器格式
const transformCategories = (categories: ProductCategory[]): CascaderOption[] => {
  return categories.map(category => ({
    value: category.catId,
    label: category.catName,
    isLeaf: category.isLeaf,
    loading: false,
    children: category.isLeaf ? undefined : []
  }))
}

// 初始化加载根分类
const initializeCategories = async () => {
  if (!props.shopId) {
    console.warn('[CategoryCascader] shopId 为空，无法加载分类')
    return
  }

  console.info('[CategoryCascader] 开始初始化分类，shopId:', props.shopId)
  loading.value = true
  try {
    const categories = await loadCategories()
    console.info('[CategoryCascader] 获取到根分类数据:', categories)
    options.value = transformCategories(categories)
    console.info('[CategoryCascader] 转换后的选项:', options.value)
  } catch (error) {
    console.error('[CategoryCascader] 初始化分类失败:', error)
  } finally {
    loading.value = false
  }
}

// 动态加载子分类
const loadData = async (selectedOptions: CascaderOption[]) => {
  const targetOption = selectedOptions[selectedOptions.length - 1]
  
  if (targetOption.isLeaf) {
    return
  }

  targetOption.loading = true

  try {
    const categories = await loadCategories(targetOption.value)
    targetOption.children = transformCategories(categories)
  } catch (error) {
    console.error('[CategoryCascader] 加载子分类失败:', error)
  } finally {
    targetOption.loading = false
  }
}

// 选择变化处理
const handleChange = (value: number[], selectedOptions: CascaderOption[]) => {
  console.info('[CategoryCascader] 分类选择变化:', { value, selectedOptions })

  // 确保 selectedOptions 有正确的数据结构
  if (selectedOptions && selectedOptions.length > 0) {
    console.info('[CategoryCascader] 触发 change 事件:', {
      value,
      selectedOptions: selectedOptions.map(opt => ({
        value: opt.value,
        label: opt.label,
        isLeaf: opt.isLeaf
      }))
    })
    emit('change', value, selectedOptions)
  } else {
    console.warn('[CategoryCascader] selectedOptions 为空或无效:', selectedOptions)
    emit('change', value, [])
  }
}

// 监听 shopId 变化
watch(() => props.shopId, (newShopId) => {
  if (newShopId) {
    initializeCategories()
  }
}, { immediate: true })

// 显示路径
const displayRender = ({ labels }: { labels: string[] }) => {
  return labels.join(' / ')
}

// 过滤选项
const filter = (inputValue: string, path: CascaderOption[]) => {
  return path.some(option => option.label.toLowerCase().includes(inputValue.toLowerCase()))
}
</script>

<template>
  <a-cascader
    v-model:value="selectedValue"
    :options="options"
    :load-data="loadData"
    :placeholder="placeholder"
    :loading="loading"
    :display-render="displayRender"
    :filter="filter"
    :show-search="true"
    change-on-select
    expand-trigger="hover"
    @change="handleChange"
    class="w-full"
  >
    <template #suffixIcon>
      <DownOutlined />
    </template>
    
    <template #notFoundContent>
      <div class="text-center py-4 text-gray-500">
        <div v-if="!shopId">请先选择店铺账号</div>
        <div v-else-if="loading">加载中...</div>
        <div v-else>暂无分类数据</div>
      </div>
    </template>
  </a-cascader>
</template>



<style scoped>
:deep(.ant-cascader-picker) {
  width: 100%;
}

:deep(.ant-cascader-menu) {
  max-height: 300px;
}

:deep(.ant-cascader-menu-item-loading-icon) {
  margin-right: 8px;
}
</style>
