import { ref, computed } from 'vue'

// 用户信息接口
interface User {
  username: string
  level: string
  points: number
  shopCount: number
  maxShops: number
}

// 登录凭据接口
interface LoginCredentials {
  username: string
  password: string
}

// 全局状态
const isLoggedIn = ref(false)
const currentUser = ref<User | null>(null)



// 从 localStorage 恢复登录状态
const restoreAuthState = () => {
  const savedAuth = localStorage.getItem('auth_state')
  if (savedAuth) {
    try {
      const authData = JSON.parse(savedAuth)
      if (authData.isLoggedIn && authData.user) {
        isLoggedIn.value = true
        currentUser.value = authData.user
      }
    } catch (error) {
      console.error('Failed to restore auth state:', error)
      localStorage.removeItem('auth_state')
    }
  }
}

// 保存登录状态到 localStorage
const saveAuthState = () => {
  const authData = {
    isLoggedIn: isLoggedIn.value,
    user: currentUser.value
  }
  localStorage.setItem('auth_state', JSON.stringify(authData))
}

// 登录函数
const login = async (credentials: LoginCredentials): Promise<{ success: boolean; message: string }> => {
  // TODO: 实现真实的登录逻辑，调用后端 API
  // 目前返回未实现状态
  return {
    success: false,
    message: '登录功能尚未实现，请联系开发人员'
  }
}

// 登出函数
const logout = () => {
  isLoggedIn.value = false
  currentUser.value = null
  localStorage.removeItem('auth_state')
}

// 检查是否已登录
const checkAuth = computed(() => isLoggedIn.value)

// 获取当前用户信息
const getUserInfo = computed(() => currentUser.value)

// 初始化时恢复状态
restoreAuthState()

export function useAuth() {
  return {
    // 状态
    isLoggedIn: checkAuth,
    user: getUserInfo,
    
    // 方法
    login,
    logout,
    restoreAuthState
  }
}
