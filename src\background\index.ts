// Sample code if using extensionpay.com
// import { extPay } from 'src/utils/payment/extPay'
// extPay.startBackground()

chrome.runtime.onInstalled.addListener(async (opt) => {
  // Check if reason is install or update. Eg: opt.reason === 'install' // If extension is installed.
  // opt.reason === 'update' // If extension is updated.
  if (opt.reason === "install") {
    // 首次安装时，打开 side-panel 而不是 setup 页面
    try {
      // 尝试打开 side panel
      await chrome.sidePanel.open({ windowId: (await chrome.windows.getCurrent()).id })
    } catch (error) {
      // 如果 side panel 不可用，则打开 setup 页面
      chrome.tabs.create({
        active: true,
        url: chrome.runtime.getURL("src/ui/setup/index.html#/setup/install"),
      })
    }

    return
  }

  if (opt.reason === "update") {
    // 更新时，也打开 side-panel 而不是 setup 页面
    try {
      // 尝试打开 side panel
      await chrome.sidePanel.open({ windowId: (await chrome.windows.getCurrent()).id })
    } catch (error) {
      // 如果 side panel 不可用，则不做任何操作
      console.info('Side panel not available, skipping auto-open')
    }

    return
  }
})

self.onerror = function (message, source, lineno, colno, error) {
  console.info("Error: " + message)
  console.info("Source: " + source)
  console.info("Line: " + lineno)
  console.info("Column: " + colno)
  console.info("Error object: " + error)
}

console.info("hello world from background")

// Temu 数据获取服务
class TemuBackgroundService {
  // 获取 Temu 店铺信息
  async getTemuShopInfo(): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.info('[Background] 开始获取 Temu 店铺信息...')

      // 1. 查找 Temu 商家后台标签页
      const temuTab = await this.findTemuSellerTab()
      if (!temuTab) {
        return {
          success: false,
          error: '未找到 Temu 商家后台标签页，请先打开并登录 Temu 商家后台'
        }
      }

      console.info('[Background] 找到 Temu 标签页:', temuTab.url)

      // 2. 向 content script 发送消息获取数据
      try {
        const response = await chrome.tabs.sendMessage(temuTab.id!, {
          action: 'GET_TEMU_INFO'
        })

        if (response && response.success) {
          console.info('[Background] 成功获取 Temu 信息:', response.data)
          return {
            success: true,
            data: response.data
          }
        } else {
          return {
            success: false,
            error: response?.error || '获取 Temu 信息失败'
          }
        }
      } catch (messageError) {
        console.warn('[Background] Content script 通信失败，尝试注入脚本...')

        // 3. 如果 content script 不可用，尝试注入脚本
        return await this.injectAndGetTemuInfo(temuTab.id!)
      }
    } catch (error) {
      console.error('[Background] 获取 Temu 店铺信息失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  // 查找 Temu 商家后台标签页
  private async findTemuSellerTab(): Promise<chrome.tabs.Tab | null> {
    const tabs = await chrome.tabs.query({})

    const temuDomains = [
      'seller.temu.com',
      'seller.kuajingmaihuo.com',
      'seller-cn.temu.com',
      'agentseller.temu.com',
      'agentseller-us.temu.com'
    ]

    for (const tab of tabs) {
      if (tab.url) {
        const url = new URL(tab.url)
        if (temuDomains.some(domain => url.hostname.includes(domain))) {
          return tab
        }
      }
    }

    return null
  }

  // 注入脚本并获取 Temu 信息
  private async injectAndGetTemuInfo(tabId: number): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.info('[Background] 注入脚本获取 Temu 信息...')

      // 注入脚本到页面
      const results = await chrome.scripting.executeScript({
        target: { tabId },
        func: this.getTemuInfoFromPage
      })

      if (results && results[0] && results[0].result) {
        const result = results[0].result
        if (result.success) {
          console.info('[Background] 注入脚本成功获取数据:', result.data)
          return result
        } else {
          return {
            success: false,
            error: result.error || '注入脚本执行失败'
          }
        }
      } else {
        return {
          success: false,
          error: '注入脚本无返回结果'
        }
      }
    } catch (error) {
      console.error('[Background] 注入脚本失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '注入脚本失败'
      }
    }
  }

  // 在页面中执行的函数（会被注入到 Temu 页面）
  private getTemuInfoFromPage(): { success: boolean; data?: any; error?: string } {
    try {
      console.info('[Injected Script] 开始获取 Temu 信息...')

      // 获取 anti-content 头部
      const getAntiContent = (): string | null => {
        try {
          // 方法1: 从全局变量获取
          const win = window as any
          if (win.antiContent) return win.antiContent
          if (win.__INITIAL_STATE__?.antiContent) return win.__INITIAL_STATE__.antiContent

          // 方法2: 从脚本中搜索
          const scripts = Array.from(document.querySelectorAll('script'))
          for (const script of scripts) {
            const content = script.textContent || script.innerHTML
            const patterns = [
              /["']anti-content["']\s*:\s*["']([^"']+)["']/i,
              /antiContent\s*:\s*["']([^"']+)["']/i,
              /"anti-content":\s*"([^"]+)"/i
            ]

            for (const pattern of patterns) {
              const match = content.match(pattern)
              if (match) return match[1]
            }
          }
          return null
        } catch (error) {
          console.warn('[Injected Script] 获取 anti-content 失败:', error)
          return null
        }
      }

      // 调用 Temu API
      const antiContent = getAntiContent()
      console.info('[Injected Script] Anti-content:', antiContent ? '已获取' : '未找到')

      const headers: Record<string, string> = {
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Content-Type': 'application/json',
        'Cache-Control': 'max-age=0'
      }

      if (antiContent) {
        headers['anti-content'] = antiContent
      }

      // 返回 Promise（注意：这个函数会在页面上下文中执行）
      return fetch('https://seller.kuajingmaihuo.com/bg/quiet/api/mms/userInfo', {
        method: 'POST',
        credentials: 'include',
        headers,
        body: JSON.stringify({})
      })
      .then(response => response.json())
      .then(data => {
        console.info('[Injected Script] API 响应:', data)
        if (data.success && data.result) {
          return {
            success: true,
            data: data.result
          }
        } else {
          return {
            success: false,
            error: 'API 返回数据格式错误'
          }
        }
      })
      .catch(error => {
        console.error('[Injected Script] API 调用失败:', error)
        return {
          success: false,
          error: error.message || 'API 调用失败'
        }
      })
    } catch (error) {
      console.error('[Injected Script] 执行失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '执行失败'
      }
    }
  }
}

// 创建服务实例
const temuService = new TemuBackgroundService()

// 监听来自 side panel 的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.info('[Background] 收到消息:', request)

  if (request.action === 'GET_TEMU_SHOP_INFO') {
    // 异步获取 Temu 店铺信息
    temuService.getTemuShopInfo()
      .then(result => {
        console.info('[Background] 获取结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] 获取失败:', error)
        sendResponse({
          success: false,
          error: error.message || '获取失败'
        })
      })

    // 返回 true 表示异步响应
    return true
  }

  return false
})

export {}
