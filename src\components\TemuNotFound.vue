<script setup lang="ts">
import { ref, computed } from 'vue'
import { useShopBinding } from '../composables/useShopBinding'
import { useNotification } from '../composables/useNotification'
import shopBindingService from '../services/shopBindingService'
import temuDetectionService from '../services/temuDetectionService'

// 定义 props
interface Props {
  showBindingMode?: boolean
  temuUserInfo?: any
  temuShopInfo?: any
}

const props = withDefaults(defineProps<Props>(), {
  showBindingMode: false,
  temuUserInfo: null,
  temuShopInfo: null
})

// 定义 emits
const emit = defineEmits<{
  'login-success': []
  'binding-success': []
}>()

// 使用店铺绑定状态管理
const {
  openTemuBackend,
  temuSiteInfo,
  bindShopToBackend,
  checkBackendBindingStatus
} = useShopBinding()

// 使用通知系统
const { success, error: showError, warning, info } = useNotification()

// 店铺选择选项
const shopOptions = ref([
  { value: '', label: '请选择店铺' },
  { value: 'temu_001', label: 'Temu测试店铺1' },
  { value: 'temu_002', label: 'Temu测试店铺2' }
])

const selectedShop = ref('')

// 加载状态
const isChecking = ref(false)

// 检查是否为开发环境
const isDev = computed(() => import.meta.env.DEV)

// 当前选中的店铺
const currentShop = computed(() => temuSiteInfo.value)

// 处理打开 Temu 商家后台
const handleOpenTemuBackend = () => {
  openTemuBackend()
}

// 处理重新检测
const handleRecheck = async () => {
  if (isChecking.value) {
    warning('检测进行中', '请等待当前检测完成')
    return
  }

  isChecking.value = true

  try {
    console.log('[TemuNotFound] 开始重新检测 Temu 店铺...')
    info('开始检测', '正在获取 Temu 店铺信息...')

    // 直接调用 handleTestAPI 的逻辑
    await handleTestAPI()

  } catch (err) {
    console.error('[TemuNotFound] 重新检测失败:', err)
    showError('检测失败', '网络错误或系统异常，请稍后再试')
  } finally {
    isChecking.value = false
  }
}

// 检查存储状态（开发环境）
const handleCheckStorage = async () => {
  try {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      const result = await chrome.storage.local.get(['temuSiteInfo', 'shop_binding_state'])
      console.log('Chrome Storage 内容:', result)
      info('存储检查', '存储内容已输出到控制台，请按 F12 查看')
    } else {
      const localData = localStorage.getItem('shop_binding_state')
      console.log('LocalStorage 内容:', localData)
      info('存储检查', '存储内容已输出到控制台，请按 F12 查看')
    }
  } catch (err) {
    console.error('检查存储失败:', err)
    showError('检查失败', '无法访问存储')
  }
}

// 检查后端绑定状态
const handleCheckBackendStatus = async () => {
  try {
    info('检查中', '正在检查后端绑定状态...')

    const result = await checkBackendBindingStatus()

    if (result.success) {
      if (result.data?.isTemuBound) {
        success('已绑定', `后端已绑定店铺：${result.data.temuSiteInfo?.shopName || '未知'}`)
      } else {
        warning('未绑定', '后端显示未绑定任何店铺')
      }
    } else {
      showError('检查失败', result.message || '无法检查后端绑定状态')
    }
  } catch (err) {
    console.error('检查后端状态失败:', err)
    showError('检查失败', '检查后端绑定状态时发生错误')
  }
}

// 获取页面中的 anti-content 头部
const getAntiContentHeader = (): string | null => {
  try {
    // 方法1: 尝试从页面的全局变量中获取
    const win = window as unknown as Record<string, unknown>

    // 检查常见的全局变量
    if (win.antiContent && typeof win.antiContent === 'string') {
      console.log('[TemuNotFound] 从 window.antiContent 获取到 anti-content')
      return win.antiContent
    }

    if (win.__INITIAL_STATE__) {
      const initialState = win.__INITIAL_STATE__ as Record<string, unknown>
      if (initialState.antiContent && typeof initialState.antiContent === 'string') {
        console.log('[TemuNotFound] 从 __INITIAL_STATE__ 获取到 anti-content')
        return initialState.antiContent
      }
    }

    // 方法2: 从页面脚本中搜索
    const scripts = Array.from(document.querySelectorAll('script'))
    for (const script of scripts) {
      const content = script.textContent || script.innerHTML

      // 搜索多种可能的模式
      const patterns = [
        /["']anti-content["']\s*:\s*["']([^"']+)["']/i,
        /antiContent\s*:\s*["']([^"']+)["']/i,
        /anti_content\s*:\s*["']([^"']+)["']/i,
        /"anti-content":\s*"([^"]+)"/i,
        /'anti-content':\s*'([^']+)'/i
      ]

      for (const pattern of patterns) {
        const match = content.match(pattern)
        if (match) {
          console.log('[TemuNotFound] 从脚本中获取到 anti-content')
          return match[1]
        }
      }
    }

    // 方法3: 尝试从 meta 标签获取
    const metaTags = Array.from(document.querySelectorAll('meta'))
    for (const meta of metaTags) {
      if (meta.name === 'anti-content' || meta.getAttribute('name') === 'anti-content') {
        const content = meta.content || meta.getAttribute('content')
        if (content) {
          console.log('[TemuNotFound] 从 meta 标签获取到 anti-content')
          return content
        }
      }
    }

    console.warn('[TemuNotFound] 未找到 anti-content 头部')
    return null
  } catch (error) {
    console.warn('[TemuNotFound] 获取 anti-content 头部时出错:', error)
    return null
  }
}



// 通过新的检测服务获取 Temu 信息
const getTemuInfoFromDetectionService = async (): Promise<any> => {
  try {
    console.info('[TemuNotFound] 使用检测服务获取 Temu 信息...')

    // 检查 Chrome 扩展 API 是否可用
    if (typeof chrome === 'undefined' || !chrome.scripting) {
      throw new Error('Chrome 扩展 API 不可用，请确保在扩展环境中运行')
    }

    const result = await temuDetectionService.getTemuShopData()

    if (result.success && result.data) {
      console.info('[TemuNotFound] 检测服务获取数据成功:', result.data)
      return {
        success: true,
        data: result.data
      }
    } else {
      throw new Error(result.error || '获取 Temu 信息失败')
    }
  } catch (error) {
    console.error('[TemuNotFound] 检测服务获取失败:', error)

    // 如果是 Chrome API 相关错误，提供更友好的错误信息
    if (error instanceof Error && error.message && error.message.includes('scripting')) {
      throw new Error('Chrome 扩展权限不足，请重新加载扩展或检查权限配置')
    }

    throw error
  }
}

// 处理从 content script 获取到的 Temu 店铺信息
const processTemuShopInfo = async (temuData: any) => {
  try {
    // 设置用户ID到绑定服务
    if (temuData.userId) {
      shopBindingService.setUserId(String(temuData.userId))
      console.log('[TemuNotFound] 设置用户ID:', temuData.userId)
    }

    // 设置店铺ID到绑定服务
    let shopId = null
    if (temuData.companyList && Array.isArray(temuData.companyList)) {
      for (const company of temuData.companyList) {
        if (company.malInfoList && Array.isArray(company.malInfoList)) {
          for (const mallInfo of company.malInfoList) {
            if (mallInfo.mallId) {
              shopId = String(mallInfo.mallId)
              break
            }
          }
          if (shopId) break
        }
      }
    }

    if (shopId) {
      shopBindingService.setShopId(shopId)
      console.log('[TemuNotFound] 设置店铺ID:', shopId)
    }

    if (temuData.companyList && Array.isArray(temuData.companyList)) {
      // 解析店铺信息
      for (const company of temuData.companyList) {
        if (company.malInfoList && Array.isArray(company.malInfoList)) {
          for (const mallInfo of company.malInfoList) {
            if (mallInfo.mallId && mallInfo.mallName) {
              const temuSiteInfo = {
                fromPlat: 'temu',
                isSemiManagedMall: mallInfo.isSemiManagedMall || false,
                logo: mallInfo.logo || '',
                mallId: mallInfo.mallId,
                mallName: mallInfo.mallName,
                mallStatus: mallInfo.mallStatus || 1,
                shopId: mallInfo.mallId,
                shopName: mallInfo.mallName
              }

              // 保存到本地存储
              if (typeof chrome !== 'undefined' && chrome.storage) {
                await chrome.storage.local.set({ temuSiteInfo })
                console.log('[TemuNotFound] 数据已保存到 Chrome Storage:', temuSiteInfo)
              } else {
                localStorage.setItem('temuSiteInfo', JSON.stringify(temuSiteInfo))
                console.log('[TemuNotFound] 数据已保存到 LocalStorage:', temuSiteInfo)
              }

              // 尝试绑定到后端
              try {
                info('绑定中', '正在将店铺信息绑定到后端...')
                const bindResult = await bindShopToBackend(temuSiteInfo)

                if (bindResult.success) {
                  success('绑定成功！', `店铺：${mallInfo.mallName}，正在跳转到工作台...`)
                  // 发出绑定成功事件
                  emit('binding-success')
                } else {
                  warning('本地绑定成功', `店铺：${mallInfo.mallName}，但后端绑定失败：${bindResult.message}`)
                  // 即使后端绑定失败，本地绑定成功也算成功
                  emit('binding-success')
                }
              } catch (bindError) {
                console.error('[TemuNotFound] 后端绑定失败:', bindError)
                warning('本地绑定成功', `店铺：${mallInfo.mallName}，但后端绑定失败`)
                // 即使后端绑定失败，本地绑定成功也算成功
                emit('binding-success')
              }

              return
            }
          }
        }
      }
      warning('数据解析', '未在返回数据中找到店铺信息')
    } else {
      warning('数据格式', '返回数据格式不正确')
    }
  } catch (error) {
    console.error('[TemuNotFound] 处理店铺信息失败:', error)
    showError('处理失败', '处理店铺信息时发生错误')
  }
}

// 手动调用 API 测试
const handleTestAPI = async () => {
  try {
    info('API 测试', '正在通过 content script 获取 Temu 信息...')

    // 首先检查是否在 Chrome 扩展环境中
    if (typeof chrome === 'undefined' || !chrome.tabs) {
      // 如果不在扩展环境中，尝试直接调用（降级方案）
      warning('环境检测', '不在 Chrome 扩展环境中，尝试直接调用 API')
      await handleDirectAPICall()
      return
    }

    try {
      // 通过检测服务获取 Temu 信息
      const temuInfo = await getTemuInfoFromDetectionService()

      if (temuInfo && temuInfo.success && temuInfo.data) {
        console.log('[TemuNotFound] 从 content script 获取到 Temu 信息:', temuInfo.data)

        // 处理获取到的店铺信息
        await processTemuShopInfo(temuInfo.data)
      } else {
        throw new Error(temuInfo?.error || '未获取到有效的 Temu 信息')
      }
    } catch (contentScriptError) {
      console.warn('[TemuNotFound] Content script 获取失败:', contentScriptError)

      // 根据错误类型提供不同的提示
      if (contentScriptError instanceof Error && contentScriptError.message) {
        if (contentScriptError.message.includes('权限')) {
          showError('权限错误', '扩展权限不足，请重新加载扩展后重试')
        } else if (contentScriptError.message.includes('scripting')) {
          showError('API 错误', 'Chrome 扩展 API 不可用，请检查扩展配置')
        } else {
          warning('获取失败', '请确保您在 Temu 商家后台页面，然后重试')
        }
      } else {
        warning('获取失败', '请确保您在 Temu 商家后台页面，然后重试')
      }
    }
  } catch (err) {
    console.error('[TemuNotFound] API 测试失败:', err)
    showError('API 测试失败', '请确保已登录 Temu 商家后台')
  }
}

// 直接调用 API（降级方案）
const handleDirectAPICall = async () => {
  try {
    // 获取 anti-content 头部
    const antiContent = getAntiContentHeader()
    console.log('[TemuNotFound] Anti-content 头部:', antiContent ? '已获取' : '未找到')

    // 构建请求头（模拟正确的浏览器请求）
    const headers: Record<string, string> = {
      'Accept': '*/*',
      'Accept-Language': 'zh-CN,zh;q=0.9',
      'Content-Type': 'application/json',
      'Cache-Control': 'max-age=0',
      'Origin': 'https://seller.kuajingmaihuo.com',
      'Referer': 'https://seller.kuajingmaihuo.com/settle/site-main'
    }

    // 如果找到了 anti-content，添加到请求头中
    if (antiContent) {
      headers['anti-content'] = antiContent
    }

    const response = await fetch('https://seller.kuajingmaihuo.com/bg/quiet/api/mms/userInfo', {
      method: 'POST',
      credentials: 'include',
      headers,
      body: JSON.stringify({})
    })

    if (response.ok) {
      const data = await response.json()
      console.log('API 返回数据:', data)

      if (data.success && data.result && data.result.companyList) {
        // 解析店铺信息
        for (const company of data.result.companyList) {
          if (company.malInfoList && Array.isArray(company.malInfoList)) {
            for (const mallInfo of company.malInfoList) {
              if (mallInfo.mallId && mallInfo.mallName) {
                const temuSiteInfo = {
                  fromPlat: 'temu',
                  isSemiManagedMall: mallInfo.isSemiManagedMall || false,
                  logo: mallInfo.logo || '',
                  mallId: mallInfo.mallId,
                  mallName: mallInfo.mallName,
                  mallStatus: mallInfo.mallStatus || 1,
                  shopId: mallInfo.mallId,
                  shopName: mallInfo.mallName
                }

                // 保存到本地存储
                if (typeof chrome !== 'undefined' && chrome.storage) {
                  await chrome.storage.local.set({ temuSiteInfo })
                  console.log('[TemuNotFound] 数据已保存到 Chrome Storage:', temuSiteInfo)
                } else {
                  localStorage.setItem('temuSiteInfo', JSON.stringify(temuSiteInfo))
                  console.log('[TemuNotFound] 数据已保存到 LocalStorage:', temuSiteInfo)
                }

                // 尝试绑定到后端
                try {
                  info('绑定中', '正在将店铺信息绑定到后端...')
                  const bindResult = await bindShopToBackend(temuSiteInfo)

                  if (bindResult.success) {
                    success('绑定成功！', `店铺：${mallInfo.mallName}，正在跳转到工作台...`)
                    // 发出绑定成功事件
                    emit('binding-success')
                  } else {
                    warning('本地绑定成功', `店铺：${mallInfo.mallName}，但后端绑定失败：${bindResult.message}`)
                    // 即使后端绑定失败，本地绑定成功也算成功
                    emit('binding-success')
                  }
                } catch (bindError) {
                  console.error('[TemuNotFound] 后端绑定失败:', bindError)
                  warning('本地绑定成功', `店铺：${mallInfo.mallName}，但后端绑定失败`)
                  // 即使后端绑定失败，本地绑定成功也算成功
                  emit('binding-success')
                }

                // 不再自动刷新页面，由路由管理器处理
                return
              }
            }
          }
        }
        warning('API 测试', '未在返回数据中找到店铺信息')
      } else {
        warning('API 测试', 'API 返回格式不正确')
      }
    } else {
      showError('API 测试失败', `状态码: ${response.status}`)
    }
  } catch (err) {
    console.error('API 测试失败:', err)
    showError('API 测试失败', '请确保已登录 Temu 商家后台')
  }
}
</script>

<template>
  <div class="w-full min-w-[900px] p-5">
    <div class="ant-space css-1p3hq3p ant-space-vertical width-100" style="gap: 8px;">
      <!-- 顶部标题和店铺选择 -->
      <div class="ant-space-item">
        <div class="display-align-items justify-content-space-between">
          <div class="ant-space css-1p3hq3p ant-space-horizontal ant-space-align-center" style="gap: 8px;">
            <div class="ant-space-item">
              <h2 class="ant-typography css-1p3hq3p text-2xl font-bold">Temu</h2>
            </div>
            <div class="ant-space-item">
              <select 
                v-model="selectedShop"
                class="ant-select css-1p3hq3p ant-select-single ant-select-show-arrow min-w-[180px] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option v-for="option in shopOptions" :key="option.value" :value="option.value">
                  {{ option.label }}
                </option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <!-- 错误提示区域 -->
      <div class="ant-space-item">
        <div
          role="alert"
          :class="[
            'ant-alert ant-alert-no-icon css-1p3hq3p rounded-md p-4',
            showBindingMode
              ? 'ant-alert-warning bg-yellow-50 border border-yellow-200'
              : 'ant-alert-error bg-red-50 border border-red-200'
          ]"
          data-show="true"
        >
          <div class="ant-alert-content">
            <div class="ant-alert-message">
              <div class="display-align-items justify-content-space-between">
                <span :class="showBindingMode ? 'text-yellow-800 font-medium' : 'text-red-800 font-medium'">
                  {{ showBindingMode ? '需要绑定店铺' : '页面未找到' }}
                </span>
                <div class="ant-space css-1p3hq3p ant-space-horizontal ant-space-align-center" style="gap: 8px;">
                  <div class="ant-space-item">
                    <button 
                      @click="handleOpenTemuBackend"
                      class="css-1p3hq3p ant-btn ant-btn-primary bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium transition-colors"
                      type="button"
                    >
                      <span>打开Temu商家后台</span>
                    </button>
                  </div>
                  <div class="ant-space-item">
                    <button
                      @click="handleRecheck"
                      :disabled="isChecking"
                      :class="[
                        'css-1p3hq3p ant-btn ant-btn-default px-4 py-2 rounded-md font-medium border transition-colors',
                        isChecking
                          ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                          : 'bg-white hover:bg-gray-50 text-gray-700 border-gray-300'
                      ]"
                      type="button"
                    >
                      <span v-if="isChecking" class="flex items-center">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        检测中...
                      </span>
                      <span v-else>再次重试</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Temu 用户信息显示（绑定模式） -->
    <div v-if="showBindingMode && temuUserInfo" class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
      <h4 class="text-sm font-semibold text-blue-800 mb-2">✅ Temu 登录信息</h4>
      <div class="text-sm text-blue-700 space-y-1">
        <p><strong>用户ID：</strong>{{ temuUserInfo.userId }}</p>
        <p v-if="temuUserInfo.userName"><strong>用户名：</strong>{{ temuUserInfo.userName }}</p>
        <p v-if="temuUserInfo.email"><strong>邮箱：</strong>{{ temuUserInfo.email }}</p>
      </div>
      <div v-if="temuShopInfo" class="mt-3 pt-3 border-t border-blue-200">
        <h5 class="text-sm font-semibold text-blue-800 mb-1">店铺信息</h5>
        <div class="text-sm text-blue-700 space-y-1">
          <p><strong>店铺名称：</strong>{{ temuShopInfo.shopName }}</p>
          <p><strong>店铺ID：</strong>{{ temuShopInfo.mallId }}</p>
        </div>
      </div>
    </div>

    <!-- 额外的说明信息 -->
    <div class="mt-8 p-6 bg-gray-50 rounded-lg">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">
        {{ showBindingMode ? '如何完成店铺绑定？' : '如何绑定 Temu 店铺？' }}
      </h3>
      <div class="space-y-3 text-gray-700">
        <div v-if="!showBindingMode" class="flex items-start">
          <span class="inline-flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-600 rounded-full text-sm font-medium mr-3 mt-0.5">1</span>
          <p>点击上方"打开Temu商家后台"按钮，登录您的 Temu 商家账号</p>
        </div>
        <div v-if="!showBindingMode" class="flex items-start">
          <span class="inline-flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-600 rounded-full text-sm font-medium mr-3 mt-0.5">2</span>
          <p>确保您已经成功登录到 Temu 商家后台页面</p>
        </div>
        <div class="flex items-start">
          <span class="inline-flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-600 rounded-full text-sm font-medium mr-3 mt-0.5">
            {{ showBindingMode ? '1' : '3' }}
          </span>
          <p>{{ showBindingMode ? '点击"再次重试"按钮进行店铺检测和绑定' : '返回此页面，点击"再次重试"按钮进行店铺检测' }}</p>
        </div>
        <div class="flex items-start">
          <span class="inline-flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-600 rounded-full text-sm font-medium mr-3 mt-0.5">
            {{ showBindingMode ? '2' : '4' }}
          </span>
          <p>{{ showBindingMode ? '绑定成功后，系统将自动跳转到工作台页面' : '检测成功后，系统将自动跳转到工作台页面' }}</p>
        </div>
      </div>
    </div>

    <!-- 当前店铺信息显示 -->
    <div v-if="currentShop" class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
      <h4 class="text-sm font-semibold text-green-800 mb-2">✅ 已检测到店铺信息</h4>
      <div class="text-sm text-green-700 space-y-1">
        <p><strong>店铺名称：</strong>{{ currentShop.shopName }}</p>
        <p><strong>店铺ID：</strong>{{ currentShop.mallId }}</p>
        <p><strong>平台：</strong>{{ currentShop.fromPlat }}</p>
        <p><strong>状态：</strong>{{ currentShop.mallStatus === 1 ? '正常' : '异常' }}</p>
        <p><strong>半托管：</strong>{{ currentShop.isSemiManagedMall ? '是' : '否' }}</p>
      </div>
    </div>

    <!-- 测试功能区域 -->
    <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
      <h4 class="text-sm font-semibold text-blue-800 mb-2">🔧 调试工具</h4>
      <p class="text-sm text-blue-700 mb-3">用于测试和调试店铺检测功能：</p>
      <div class="space-x-2 space-y-2">
        <button
          @click="handleTestAPI"
          class="text-sm bg-blue-200 hover:bg-blue-300 text-blue-800 px-3 py-1 rounded transition-colors"
        >
          测试 API 调用
        </button>
        <button
          @click="handleCheckStorage"
          class="text-sm bg-purple-200 hover:bg-purple-300 text-purple-800 px-3 py-1 rounded transition-colors"
        >
          检查存储状态
        </button>
        <button
          @click="handleCheckBackendStatus"
          class="text-sm bg-green-200 hover:bg-green-300 text-green-800 px-3 py-1 rounded transition-colors"
        >
          检查后端绑定状态
        </button>

      </div>
    </div>
  </div>
</template>

<style scoped>
.display-align-items {
  display: flex;
  align-items: center;
}

.justify-content-space-between {
  justify-content: space-between;
}

.width-100 {
  width: 100%;
}

.ant-space {
  display: flex;
}

.ant-space-vertical {
  flex-direction: column;
}

.ant-space-horizontal {
  flex-direction: row;
}

.ant-space-align-center {
  align-items: center;
}

.ant-space-item {
  display: flex;
}
</style>
