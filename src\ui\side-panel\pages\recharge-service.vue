<script setup lang="ts">
import { CreditCardOutlined } from '@ant-design/icons-vue'

// 充值服务页面
</script>

<template>
  <div class="h-full flex flex-col bg-gray-50">
    <a-card :bordered="false" class="h-full shadow-sm">
      <template #title>
        <div class="flex items-center space-x-2">
          <CreditCardOutlined />
          <span class="text-lg font-semibold">充值服务</span>
        </div>
      </template>

      <div class="h-full flex items-center justify-center">
        <a-empty description="充值服务功能正在开发中...">
          <template #image>
            <CreditCardOutlined class="text-6xl text-gray-400" />
          </template>
        </a-empty>
      </div>
    </a-card>
  </div>
</template>
