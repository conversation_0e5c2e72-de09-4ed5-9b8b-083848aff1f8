// Temu 登录状态检测服务
// 检测用户是否已登录 Temu 商家后台

import { getTemuApiUrl, getApiUrl, config } from '../config'

interface TemuLoginStatus {
  isLoggedIn: boolean
  userInfo?: {
    userId: string
    userName?: string
    email?: string
  }
  shopInfo?: {
    mallId: string
    shopId: string
    mallName: string
    shopName: string
  }
  error?: string
}

class TemuLoginService {

  // 检测 Temu 登录状态
  async checkTemuLoginStatus(): Promise<TemuLoginStatus> {
    try {
      console.log('[TemuLoginService] 开始检测 Temu 登录状态...')

      // 获取 anti-content 头部
      const antiContent = this.getAntiContentHeader()
      
      // 构建请求头（模拟正确的浏览器请求）
      const headers: Record<string, string> = {
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Content-Type': 'application/json',
        'Cache-Control': 'max-age=0',
        'Origin': 'https://seller.kuajingmaihuo.com',
        'Referer': 'https://seller.kuajingmaihuo.com/settle/site-main'
      }

      // 如果找到了 anti-content，添加到请求头中
      if (antiContent) {
        headers['anti-content'] = antiContent
      }

      const response = await fetch(getTemuApiUrl(), {
        method: 'POST',
        credentials: 'include',
        headers,
        body: JSON.stringify({})
      })

      if (response.ok) {
        const data = await response.json()
        console.log('[TemuLoginService] Temu API 响应:', data)

        if (data.success && data.result) {
          // 解析用户和店铺信息
          const userInfo = this.parseUserInfo(data.result)
          const shopInfo = this.parseShopInfo(data.result)

          if (userInfo) {
            console.log('[TemuLoginService] Temu 登录状态: 已登录')
            return {
              isLoggedIn: true,
              userInfo,
              shopInfo,
            }
          }
        }
      }

      console.log('[TemuLoginService] Temu 登录状态: 未登录')
      return {
        isLoggedIn: false,
        error: '未登录或登录已过期'
      }
    } catch (error) {
      console.error('[TemuLoginService] 检测登录状态失败:', error)
      return {
        isLoggedIn: false,
        error: error instanceof Error ? error.message : '网络错误'
      }
    }
  }

  // 检测后台绑定状态
  async checkBackendBindingStatus(userId: string): Promise<{ isBound: boolean; shopInfo?: any; error?: string }> {
    try {
      console.log('[TemuLoginService] 检测后台绑定状态...')

      const response = await fetch(getApiUrl('/dmj/shop/binding/dmjList'), {
        method: 'POST',
        headers: {
          'Accept': '*/*',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          keyword: '',
          pageSize: 10,
          currentPage: 1,
          userId: userId // 添加用户ID参数
        })
      })

      if (response.ok) {
        const result = await response.json()
        
        if (result.code === 200 && result.data && result.data.length > 0) {
          console.log('[TemuLoginService] 后台绑定状态: 已绑定')
          return {
            isBound: true,
            shopInfo: result.data[0] // 返回第一个绑定的店铺
          }
        }
      }

      console.log('[TemuLoginService] 后台绑定状态: 未绑定')
      return {
        isBound: false
      }
    } catch (error) {
      console.error('[TemuLoginService] 检测绑定状态失败:', error)
      return {
        isBound: false,
        error: error instanceof Error ? error.message : '网络错误'
      }
    }
  }

  // 获取页面中的 anti-content 头部
  private getAntiContentHeader(): string | null {
    try {
      // 方法1: 尝试从页面的全局变量中获取
      const win = window as unknown as Record<string, unknown>

      // 检查常见的全局变量
      if (win.antiContent && typeof win.antiContent === 'string') {
        console.log('[TemuLoginService] 从 window.antiContent 获取到 anti-content')
        return win.antiContent
      }

      if (win.__INITIAL_STATE__) {
        const initialState = win.__INITIAL_STATE__ as Record<string, unknown>
        if (initialState.antiContent && typeof initialState.antiContent === 'string') {
          console.log('[TemuLoginService] 从 __INITIAL_STATE__ 获取到 anti-content')
          return initialState.antiContent
        }
      }

      // 方法2: 从页面脚本中搜索
      const scripts = Array.from(document.querySelectorAll('script'))
      for (const script of scripts) {
        const content = script.textContent || script.innerHTML

        // 搜索多种可能的模式
        const patterns = [
          /["']anti-content["']\s*:\s*["']([^"']+)["']/i,
          /antiContent\s*:\s*["']([^"']+)["']/i,
          /anti_content\s*:\s*["']([^"']+)["']/i,
          /"anti-content":\s*"([^"]+)"/i,
          /'anti-content':\s*'([^']+)'/i
        ]

        for (const pattern of patterns) {
          const match = content.match(pattern)
          if (match) {
            console.log('[TemuLoginService] 从脚本中获取到 anti-content')
            return match[1]
          }
        }
      }

      // 方法3: 尝试从 meta 标签获取
      const metaTags = Array.from(document.querySelectorAll('meta'))
      for (const meta of metaTags) {
        if (meta.name === 'anti-content' || meta.getAttribute('name') === 'anti-content') {
          const content = meta.content || meta.getAttribute('content')
          if (content) {
            console.log('[TemuLoginService] 从 meta 标签获取到 anti-content')
            return content
          }
        }
      }

      console.warn('[TemuLoginService] 未找到 anti-content 头部')
      return null
    } catch (error) {
      console.warn('[TemuLoginService] 获取 anti-content 头部时出错:', error)
      return null
    }
  }

  // 解析用户信息
  private parseUserInfo(data: any): { userId: string; userName?: string; email?: string } | null {
    try {
      if (data.userId) {
        return {
          userId: String(data.userId),
          userName: data.userName || data.name,
          email: data.email
        }
      }
      return null
    } catch (error) {
      console.warn('[TemuLoginService] 解析用户信息失败:', error)
      return null
    }
  }

  // 解析店铺信息
  private parseShopInfo(data: any): { mallId: string; shopId: string; mallName: string; shopName: string } | undefined {
    try {
      if (data.companyList && Array.isArray(data.companyList)) {
        for (const company of data.companyList) {
          if (company && company.malInfoList && Array.isArray(company.malInfoList)) {
            for (const mallInfo of company.malInfoList) {
              if (mallInfo && mallInfo.mallId && mallInfo.mallName) {
                return {
                  mallId: String(mallInfo.mallId),
                  shopId: String(mallInfo.mallId),
                  mallName: String(mallInfo.mallName),
                  shopName: String(mallInfo.mallName)
                }
              }
            }
          }
        }
      }
      return undefined
    } catch (error) {
      console.warn('[TemuLoginService] 解析店铺信息失败:', error)
      return undefined
    }
  }

  // 跳转到 Temu 商家后台
  openTemuBackend(): void {
    const temuUrl = 'https://seller.kuajingmaihuo.com/'
    window.open(temuUrl, '_blank')
  }
}

// 创建单例实例
export const temuLoginService = new TemuLoginService()
export default temuLoginService
