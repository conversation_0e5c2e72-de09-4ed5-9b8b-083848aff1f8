export const useTestStore = defineStore("app", () => {
  const { data: count } = useBrowserLocalStorage("count", 0)
  const { data: name } = useBrowserLocalStorage("name", "<PERSON>")

  // You should probably use chrome.storage API instead of localStorage since localStorage history can be cleared by the user.
  // See https://developer.chrome.com/docs/extensions/reference/api/storage

  const increment = () => {
    count.value++
  }

  const decrement = () => {
    count.value--
  }

  return {
    count,
    name,
    increment,
    decrement,
  }
})
