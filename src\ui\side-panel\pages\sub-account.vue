<script setup lang="ts">
import { ref, computed, reactive, watch } from 'vue'
import { Modal } from 'ant-design-vue'
import { PlusOutlined, SearchOutlined, EditOutlined, DeleteOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons-vue'

// 子账号管理页面

// 子账号数据类型
interface SubAccount {
  id: string
  loginId: string
  password: string
  createTime: string
  remark: string
  status: 'active' | 'inactive'
}

// 子账号列表数据
const subAccounts = ref<SubAccount[]>([
  {
    id: '1',
    loginId: '<EMAIL>',
    password: 'password123',
    createTime: '2024-01-15 10:30:00',
    remark: '测试账号1',
    status: 'active'
  },
  {
    id: '2',
    loginId: '<EMAIL>',
    password: 'password456',
    createTime: '2024-01-16 14:20:00',
    remark: '测试账号2',
    status: 'inactive'
  }
])

// 表格列定义
const columns = [
  {
    title: '子账号ID(登录)',
    dataIndex: 'loginId',
    key: 'loginId',
    width: 200,
    ellipsis: true
  },
  {
    title: '密码',
    dataIndex: 'password',
    key: 'password',
    width: 150
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    ellipsis: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right'
  }
]

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 50,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) => `共 ${total} 条记录，显示 ${range[0]}-${range[1]} 条`,
  pageSizeOptions: ['20', '50', '100', '200', '500']
})

// 表单数据
const formData = reactive({
  loginId: '',
  password: '',
  confirmPassword: '',
  remark: ''
})

// 编辑表单数据
const editFormData = reactive({
  id: '',
  loginId: '',
  remark: ''
})

// 弹窗状态
const showAddModal = ref(false)
const showEditModal = ref(false)
const loading = ref(false)

// 搜索关键词
const searchKeyword = ref('')

// 密码显示状态
const passwordVisible = ref<Record<string, boolean>>({})

// 表单引用
const addFormRef = ref()
const editFormRef = ref()

// 表单验证规则
const addFormRules = {
  loginId: [
    { required: true, message: '请输入登录账号', trigger: 'blur' },
    { min: 3, max: 50, message: '账号长度应在 3-50 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度应在 6-20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (_rule: any, value: string) => {
        if (value !== formData.password) {
          return Promise.reject('两次输入的密码不一致')
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ]
}

const editFormRules = {
  loginId: [
    { required: true, message: '请输入登录账号', trigger: 'blur' },
    { min: 3, max: 50, message: '账号长度应在 3-50 个字符', trigger: 'blur' }
  ]
}

// 新增子账号
const handleAdd = () => {
  showAddModal.value = true
  // 重置表单
  Object.assign(formData, {
    loginId: '',
    password: '',
    confirmPassword: '',
    remark: ''
  })
}

// 保存新账号
const handleSaveAdd = async () => {
  try {
    // 表单验证
    await addFormRef.value?.validate()

    loading.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    const newAccount: SubAccount = {
      id: Date.now().toString(),
      loginId: formData.loginId,
      password: formData.password,
      createTime: new Date().toLocaleString('zh-CN'),
      remark: formData.remark,
      status: 'active'
    }

    subAccounts.value.unshift(newAccount)
    showAddModal.value = false
    updatePagination()

    // 显示成功消息
    console.info('子账号创建成功')
  } catch (error) {
    console.error('创建子账号失败:', error)
  } finally {
    loading.value = false
  }
}

// 编辑子账号
const handleEdit = (record: SubAccount) => {
  showEditModal.value = true
  Object.assign(editFormData, {
    id: record.id,
    loginId: record.loginId,
    remark: record.remark
  })
}

// 保存编辑
const handleSaveEdit = async () => {
  try {
    // 表单验证
    await editFormRef.value?.validate()

    loading.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    const index = subAccounts.value.findIndex(acc => acc.id === editFormData.id)
    if (index !== -1) {
      subAccounts.value[index] = {
        ...subAccounts.value[index],
        loginId: editFormData.loginId,
        remark: editFormData.remark
      }
    }

    showEditModal.value = false
    console.info('子账号更新成功')
  } catch (error) {
    console.error('更新子账号失败:', error)
  } finally {
    loading.value = false
  }
}

// 删除子账号
const handleDelete = (record: SubAccount) => {
  // 使用 ant-design-vue 的确认对话框
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除子账号 "${record.loginId}" 吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    onOk: async () => {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))

        const index = subAccounts.value.findIndex(acc => acc.id === record.id)
        if (index !== -1) {
          subAccounts.value.splice(index, 1)
          updatePagination()
          console.info('子账号删除成功')
        }
      } catch (error) {
        console.error('删除子账号失败:', error)
      }
    }
  })
}

// 切换账号状态
const handleToggleStatus = async (record: SubAccount) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    record.status = record.status === 'active' ? 'inactive' : 'active'
    const statusText = record.status === 'active' ? '启用' : '禁用'
    console.info(`子账号已${statusText}`)
  } catch (error) {
    console.error('状态切换失败:', error)
  }
}

// 切换密码显示
const togglePasswordVisible = (id: string) => {
  passwordVisible.value[id] = !passwordVisible.value[id]
}

// 搜索功能
const handleSearch = () => {
  console.info('搜索关键词:', searchKeyword.value)
  // 搜索时重置到第一页
  pagination.current = 1
}

// 重置搜索
const handleReset = () => {
  searchKeyword.value = ''
  pagination.current = 1
}

// 分页变化
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

// 过滤后的数据
const filteredData = computed(() => {
  if (!searchKeyword.value) {
    return subAccounts.value
  }

  const keyword = searchKeyword.value.toLowerCase()
  return subAccounts.value.filter(account =>
    account.loginId.toLowerCase().includes(keyword) ||
    account.remark.toLowerCase().includes(keyword)
  )
})

// 更新分页信息
const updatePagination = () => {
  pagination.total = filteredData.value.length
}

// 监听过滤数据变化，更新分页
watch(filteredData, () => {
  updatePagination()
  // 如果当前页超出了总页数，重置到第一页
  const totalPages = Math.ceil(pagination.total / pagination.pageSize)
  if (pagination.current > totalPages && totalPages > 0) {
    pagination.current = 1
  }
}, { immediate: true })
</script>

<template>
  <div class="h-full flex flex-col bg-gray-50">
    <!-- 页面标题 -->
    <a-card class="mb-6 shadow-sm" :bordered="false">
      <template #title>
        <div class="flex items-center space-x-2">
          <span class="text-xl">👥</span>
          <span class="text-lg font-semibold">子账号管理</span>
        </div>
      </template>

      <!-- 操作区域 -->
      <div class="flex flex-col space-y-4">
        <!-- 操作按钮 -->
        <div class="flex items-center space-x-3">
          <a-button type="primary" @click="handleAdd">
            <template #icon>
              <PlusOutlined />
            </template>
            新增子账号
          </a-button>

          <a-button @click="handleSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>

          <a-button @click="handleReset">
            重置
          </a-button>
        </div>

        <!-- 搜索区域 -->
        <div class="flex items-center space-x-4">
          <a-input
            v-model:value="searchKeyword"
            placeholder="搜索子账号ID或备注..."
            style="width: 300px"
            @press-enter="handleSearch"
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>

          <div class="text-sm text-gray-500">
            共找到 {{ filteredData.length }} 条记录
          </div>
        </div>
      </div>
    </a-card>

    <!-- 表格区域 -->
    <div class="flex-1 overflow-hidden">
      <a-card :bordered="false" class="h-full">
        <a-table
          :columns="columns"
          :data-source="filteredData"
          :pagination="pagination"
          :loading="loading"
          :scroll="{ x: 1000 }"
          @change="handleTableChange"
          row-key="id"
        >
          <!-- 密码列 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'password'">
              <div class="flex items-center space-x-2">
                <span v-if="passwordVisible[record.id]">{{ record.password }}</span>
                <span v-else>********</span>
                <a-button
                  type="text"
                  size="small"
                  @click="togglePasswordVisible(record.id)"
                >
                  <template #icon>
                    <EyeOutlined v-if="!passwordVisible[record.id]" />
                    <EyeInvisibleOutlined v-else />
                  </template>
                </a-button>
              </div>
            </template>

            <!-- 状态列 -->
            <template v-else-if="column.key === 'status'">
              <a-tag :color="record.status === 'active' ? 'success' : 'error'">
                {{ record.status === 'active' ? '启用' : '禁用' }}
              </a-tag>
            </template>

            <!-- 操作列 -->
            <template v-else-if="column.key === 'actions'">
              <a-space>
                <a-button
                  type="primary"
                  size="small"
                  @click="handleEdit(record)"
                >
                  <template #icon>
                    <EditOutlined />
                  </template>
                  编辑
                </a-button>

                <a-button
                  :type="record.status === 'active' ? 'default' : 'primary'"
                  size="small"
                  @click="handleToggleStatus(record)"
                >
                  {{ record.status === 'active' ? '禁用' : '启用' }}
                </a-button>

                <a-button
                  type="primary"
                  danger
                  size="small"
                  @click="handleDelete(record)"
                >
                  <template #icon>
                    <DeleteOutlined />
                  </template>
                  删除
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 新增子账号弹窗 -->
    <a-modal
      v-model:open="showAddModal"
      title="新增子账号"
      :width="500"
      :confirm-loading="loading"
      @ok="handleSaveAdd"
      @cancel="showAddModal = false"
    >
      <a-form
        :model="formData"
        :rules="addFormRules"
        layout="vertical"
        ref="addFormRef"
      >
        <a-form-item
          label="登录账号"
          name="loginId"
          required
        >
          <a-input
            v-model:value="formData.loginId"
            placeholder="请输入登录账号"
            :maxlength="50"
          />
        </a-form-item>

        <a-form-item
          label="密码"
          name="password"
          required
        >
          <a-input-password
            v-model:value="formData.password"
            placeholder="请输入密码"
            :maxlength="20"
          />
        </a-form-item>

        <a-form-item
          label="确认密码"
          name="confirmPassword"
          required
        >
          <a-input-password
            v-model:value="formData.confirmPassword"
            placeholder="请再次输入密码"
            :maxlength="20"
          />
        </a-form-item>

        <a-form-item
          label="备注"
          name="remark"
        >
          <a-textarea
            v-model:value="formData.remark"
            placeholder="请输入备注信息"
            :rows="3"
            :maxlength="200"
            show-count
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 编辑子账号弹窗 -->
    <a-modal
      v-model:open="showEditModal"
      title="编辑子账号"
      :width="500"
      :confirm-loading="loading"
      @ok="handleSaveEdit"
      @cancel="showEditModal = false"
    >
      <a-form
        :model="editFormData"
        :rules="editFormRules"
        layout="vertical"
        ref="editFormRef"
      >
        <a-form-item
          label="登录账号"
          name="loginId"
          required
        >
          <a-input
            v-model:value="editFormData.loginId"
            placeholder="请输入登录账号"
            :maxlength="50"
          />
        </a-form-item>

        <a-form-item
          label="备注"
          name="remark"
        >
          <a-textarea
            v-model:value="editFormData.remark"
            placeholder="请输入备注信息"
            :rows="3"
            :maxlength="200"
            show-count
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<style scoped>
:deep(.ant-table-thead > tr > th) {
  font-weight: 600;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 12px 16px;
}

:deep(.ant-pagination) {
  margin-top: 16px;
}
</style>
