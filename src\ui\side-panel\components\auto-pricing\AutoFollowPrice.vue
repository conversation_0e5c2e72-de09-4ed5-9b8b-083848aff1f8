<script setup lang="ts">
import { ref, computed } from 'vue'
import { PlayCircleOutlined } from '@ant-design/icons-vue'
import { getSiteOptions } from '../../../../config/temuSites'

// 自动跟价管理表单
const followPriceForm = ref({
  shop: '',
  site: '',
  skcFilter: '',
  warehouseStockThreshold: '',
  profitMargin: '',
  fixedAmount: '',
  rejectPricing: false
})

// 店铺选项
const shopOptions = [
  {
    value: 'ZenithCai',
    label: 'ZenithCai',
    avatar: 'https://img.cdnfe.com/supplier-public-tag/201365d418b/060d9c4f-71ff-4da0-8b98-c6c8a8221896_300x300.jpeg'
  }
]

// 站点选项
const siteOptions = computed(() => getSiteOptions().map(site => ({
  value: site.label,
  label: `${site.label}站`
})))

// 开始自动跟价管理
const startFollowPrice = () => {
  console.log('开始自动跟价管理:', followPriceForm.value)
  alert('自动跟价管理功能已启动！')
}
</script>

<template>
  <div>
    <a-form
      :model="followPriceForm"
      layout="vertical"
      @finish="startFollowPrice"
      class="space-y-6"
    >
      <!-- 基础配置 -->
      <a-card title="基础配置" class="mb-6">
        <a-row :gutter="16">
          <!-- 店铺选择 -->
          <a-col :span="12">
            <a-form-item label="店铺">
              <a-select
                v-model:value="followPriceForm.shop"
                placeholder="选择店铺"
              >
                <a-select-option
                  v-for="shop in shopOptions"
                  :key="shop.value"
                  :value="shop.value"
                >
                  <div class="flex items-center space-x-2">
                    <img
                      :src="shop.avatar"
                      alt="店铺头像"
                      class="w-4 h-4 rounded"
                    />
                    <span>{{ shop.label }}</span>
                  </div>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 站点选择 -->
          <a-col :span="12">
            <a-form-item label="站点">
              <a-select
                v-model:value="followPriceForm.site"
                placeholder="选择站点"
              >
                <a-select-option
                  v-for="site in siteOptions"
                  :key="site.value"
                  :value="site.value"
                >
                  {{ site.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- SKC筛选 -->
        <a-form-item label="SKC筛选">
          <a-input
            v-model:value="followPriceForm.skcFilter"
            placeholder="多个查询请英文逗号、空格依次输入(选填)"
          />
        </a-form-item>
      </a-card>

      <!-- 跟价条件 -->
      <a-card title="跟价条件" class="mb-6">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="货盘库存阈值">
              <a-input-number
                v-model:value="followPriceForm.warehouseStockThreshold"
                placeholder="输入库存阈值"
                class="w-full"
              />
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="毛利率(%)">
              <a-input-number
                v-model:value="followPriceForm.profitMargin"
                placeholder="输入毛利率"
                class="w-full"
              />
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="固定金额($)">
              <a-input-number
                v-model:value="followPriceForm.fixedAmount"
                placeholder="输入固定金额"
                :step="0.01"
                class="w-full"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item>
          <a-checkbox v-model:checked="followPriceForm.rejectPricing">
            拒绝核价
          </a-checkbox>
        </a-form-item>
      </a-card>

      <!-- 跟价说明 -->
      <a-card title="跟价说明" class="mb-6">
        <a-alert
          type="info"
          show-icon
          message="自动跟价规则"
          description="系统将根据市场价格变化自动调整商品价格，确保价格竞争力。"
        />
        
        <div class="mt-4 space-y-2 text-sm text-gray-600">
          <div>• 系统会监控竞品价格变化</div>
          <div>• 根据设置的毛利率和固定金额调整价格</div>
          <div>• 可选择拒绝核价以保持价格稳定</div>
        </div>
      </a-card>

      <!-- 提交按钮 -->
      <div class="flex justify-end">
        <a-button 
          type="primary"
          html-type="submit"
          size="large"
        >
          <template #icon>
            <PlayCircleOutlined />
          </template>
          开始跟价管理
        </a-button>
      </div>
    </a-form>
  </div>
</template>
