<script setup lang="ts">
import { ref } from 'vue'
import { SettingOutlined, UserOutlined, BulbOutlined } from '@ant-design/icons-vue'

// 模拟配置数据（如果有 store 可以替换）
const isDark = ref(false)
const profile = ref({
  name: '',
  age: ''
})
const others = ref({
  awesome: false,
  counter: 0
})
</script>

<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4">
      <!-- 页面标题 -->
      <a-card class="mb-6 shadow-sm" :bordered="false">
        <template #title>
          <div class="flex items-center space-x-2">
            <SettingOutlined />
            <span class="text-xl font-semibold">扩展设置</span>
          </div>
        </template>

        <p class="text-gray-600">
          您可以在这里配置与此扩展相关的各种选项。这些选项/设置是持久的，在所有上下文中都可用。
        </p>
      </a-card>

      <!-- 设置表单 -->
      <div class="space-y-6">
        <!-- 用户界面设置 -->
        <a-card title="用户界面" class="shadow-sm" :bordered="false">
          <template #extra>
            <BulbOutlined />
          </template>

          <p class="text-gray-600 mb-4">更改应用程序界面设置。</p>

          <a-form layout="vertical">
            <a-form-item label="主题模式">
              <a-switch
                v-model:checked="isDark"
                checked-children="深色"
                un-checked-children="浅色"
              />
            </a-form-item>
          </a-form>
        </a-card>

        <!-- 个人资料设置 -->
        <a-card title="个人资料" class="shadow-sm" :bordered="false">
          <template #extra>
            <UserOutlined />
          </template>

          <p class="text-gray-600 mb-4">更改您的姓名和年龄。</p>

          <a-form layout="vertical">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="姓名">
                  <a-input
                    v-model:value="profile.name"
                    placeholder="请输入姓名"
                  />
                </a-form-item>
              </a-col>

              <a-col :span="12">
                <a-form-item label="年龄">
                  <a-input-number
                    v-model:value="profile.age"
                    placeholder="请输入年龄"
                    :min="1"
                    :max="120"
                    class="w-full"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-card>

        <!-- 其他设置 -->
        <a-card title="其他设置" class="shadow-sm" :bordered="false">
          <p class="text-gray-600 mb-4">与扩展使用相关的其他设置。</p>

          <a-form layout="vertical">
            <a-form-item label="启用高级功能">
              <a-switch
                v-model:checked="others.awesome"
                checked-children="开启"
                un-checked-children="关闭"
              />
            </a-form-item>

            <a-form-item label="计数器">
              <a-input-number
                v-model:value="others.counter"
                :min="0"
                class="w-full"
              />
            </a-form-item>
          </a-form>
        </a-card>

        <!-- 说明信息 -->
        <a-card class="shadow-sm" :bordered="false">
          <a-alert
            type="info"
            show-icon
            message="提示"
            description="您也可以将此设置页面制作成组件，然后在任何上下文中使用，如弹窗、开发者工具界面等。请根据您的需求随意更改分组或选项。"
          />
        </a-card>
      </div>
    </div>
  </div>
</template>
