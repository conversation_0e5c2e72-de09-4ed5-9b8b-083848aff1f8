<script setup lang="ts">
import { onMounted } from 'vue'
import TopNavbar from '../../components/TopNavbar.vue'
import TemuNotFound from '../../components/TemuNotFound.vue'
import { useAuth } from '../../composables/useAuth'
import { useShopBinding } from '../../composables/useShopBinding'
import { useAppRouter, AppPageState } from '../../composables/useAppRouter'

// 使用登录状态管理
const { isLoggedIn } = useAuth()

// 使用店铺绑定状态管理
const { isTemuBound, bindTemuShop } = useShopBinding()

// 使用应用路由管理
const {
  currentPageState,
  isLoading,
  temuLoginStatus,
  backendBindingStatus,
  checkAppState,
  handleBindingSuccess,
  handleLoginSuccess,
  getStateDescription
} = useAppRouter()



// 处理绑定成功事件
const onBindingSuccess = () => {
  handleBindingSuccess()
}

// 处理登录成功事件
const onLoginSuccess = () => {
  handleLoginSuccess()
}

// 组件挂载时检测应用状态
onMounted(() => {
  checkAppState()
})
</script>

<template>
  <div class="h-screen flex flex-col bg-gray-50">
    <!-- 加载状态 -->
    <div v-if="currentPageState === AppPageState.LOADING" class="h-full flex items-center justify-center">
      <div class="text-center">
        <div class="inline-flex items-center">
          <svg class="animate-spin -ml-1 mr-3 h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span class="text-lg text-gray-700">{{ getStateDescription() }}</span>
        </div>
        <div class="mt-4 text-sm text-gray-500">
          正在检测 Temu 登录状态和后台绑定状态...
        </div>
      </div>
    </div>

    <!-- Temu 未登录状态 -->
    <div v-else-if="currentPageState === AppPageState.TEMU_NOT_FOUND" class="h-full">
      <TemuNotFound
        @login-success="onLoginSuccess"
      />
    </div>

    <!-- 需要绑定状态 -->
    <div v-else-if="currentPageState === AppPageState.BINDING_REQUIRED" class="h-full">
      <TemuNotFound
        :show-binding-mode="true"
        :temu-user-info="temuLoginStatus.userInfo"
        :temu-shop-info="temuLoginStatus.shopInfo"
        @binding-success="onBindingSuccess"
      />
    </div>

    <!-- 主应用页面 -->
    <div v-else-if="currentPageState === AppPageState.DASHBOARD" class="h-full flex flex-col">
      <!-- 顶部导航栏 -->
      <TopNavbar />

      <!-- 主内容区域 -->
      <div class="flex-1 overflow-hidden">
        <RouterView />
      </div>
    </div>

    <!-- 未知状态 -->
    <div v-else class="h-full flex items-center justify-center">
      <div class="text-center">
        <div class="text-red-500 text-lg mb-2">应用状态异常</div>
        <div class="text-gray-500 text-sm">{{ getStateDescription() }}</div>
        <button
          @click="checkAppState"
          class="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          重新检测
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
