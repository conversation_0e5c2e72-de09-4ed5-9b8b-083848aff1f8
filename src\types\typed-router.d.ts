/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'vue-router'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    '/action-popup/': RouteRecordInfo<'/action-popup/', '/action-popup', Record<never, never>, Record<never, never>>,
    '/action-popup/playground': RouteRecordInfo<'/action-popup/playground', '/action-popup/playground', Record<never, never>, Record<never, never>>,
    '/common/404': RouteRecordInfo<'/common/404', '/common/404', Record<never, never>, Record<never, never>>,
    '/common/about': RouteRecordInfo<'/common/about', '/common/about', Record<never, never>, Record<never, never>>,
    '/common/change-log': RouteRecordInfo<'/common/change-log', '/common/change-log', Record<never, never>, Record<never, never>>,
    '/common/features': RouteRecordInfo<'/common/features', '/common/features', Record<never, never>, Record<never, never>>,
    '/common/help': RouteRecordInfo<'/common/help', '/common/help', Record<never, never>, Record<never, never>>,
    '/common/privacy-policy': RouteRecordInfo<'/common/privacy-policy', '/common/privacy-policy', Record<never, never>, Record<never, never>>,
    '/common/terms-of-service': RouteRecordInfo<'/common/terms-of-service', '/common/terms-of-service', Record<never, never>, Record<never, never>>,
    '/content-script-iframe/': RouteRecordInfo<'/content-script-iframe/', '/content-script-iframe', Record<never, never>, Record<never, never>>,
    '/devtools-panel/': RouteRecordInfo<'/devtools-panel/', '/devtools-panel', Record<never, never>, Record<never, never>>,
    '/options-page/': RouteRecordInfo<'/options-page/', '/options-page', Record<never, never>, Record<never, never>>,
    '/setup/install': RouteRecordInfo<'/setup/install', '/setup/install', Record<never, never>, Record<never, never>>,
    '/setup/update': RouteRecordInfo<'/setup/update', '/setup/update', Record<never, never>, Record<never, never>>,
    '/side-panel/': RouteRecordInfo<'/side-panel/', '/side-panel', Record<never, never>, Record<never, never>>,
    '/side-panel/auto-pricing': RouteRecordInfo<'/side-panel/auto-pricing', '/side-panel/auto-pricing', Record<never, never>, Record<never, never>>,
    '/side-panel/dashboard': RouteRecordInfo<'/side-panel/dashboard', '/side-panel/dashboard', Record<never, never>, Record<never, never>>,
    '/side-panel/forbidden-words': RouteRecordInfo<'/side-panel/forbidden-words', '/side-panel/forbidden-words', Record<never, never>, Record<never, never>>,
    '/side-panel/local-product': RouteRecordInfo<'/side-panel/local-product', '/side-panel/local-product', Record<never, never>, Record<never, never>>,
    '/side-panel/member-service': RouteRecordInfo<'/side-panel/member-service', '/side-panel/member-service', Record<never, never>, Record<never, never>>,
    '/side-panel/product-center': RouteRecordInfo<'/side-panel/product-center', '/side-panel/product-center', Record<never, never>, Record<never, never>>,
    '/side-panel/product-center-old': RouteRecordInfo<'/side-panel/product-center-old', '/side-panel/product-center-old', Record<never, never>, Record<never, never>>,
    '/side-panel/recharge-service': RouteRecordInfo<'/side-panel/recharge-service', '/side-panel/recharge-service', Record<never, never>, Record<never, never>>,
    '/side-panel/shop-maintenance': RouteRecordInfo<'/side-panel/shop-maintenance', '/side-panel/shop-maintenance', Record<never, never>, Record<never, never>>,
    '/side-panel/sub-account': RouteRecordInfo<'/side-panel/sub-account', '/side-panel/sub-account', Record<never, never>, Record<never, never>>,
  }
}
