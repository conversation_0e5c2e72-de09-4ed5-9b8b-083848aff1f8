<script setup lang="ts">
import {
  AppstoreOutlined,
  SettingOutlined,
  ShopOutlined,
  UserOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue'

// 快速操作入口
const openSidePanel = () => {
  // 打开侧边栏面板
  chrome.sidePanel.open({ windowId: chrome.windows.WINDOW_ID_CURRENT })
}

const openOptionsPage = () => {
  // 打开选项页面
  chrome.runtime.openOptionsPage()
}
</script>

<template>
  <div class="w-80 p-4 bg-white">
    <!-- 头部信息 -->
    <a-card :bordered="false" class="mb-4">
      <div class="text-center">
        <div class="flex items-center justify-center gap-2 mb-2">
          <img src="@assets/logo.png" alt="Logo" class="w-8 h-8" />
          <h1 class="text-lg font-semibold">胡建大卖家</h1>
        </div>
        <p class="text-sm text-gray-600">店铺管理系统</p>
      </div>
    </a-card>

    <div class="space-y-3">
      <!-- 主要功能入口 -->
      <a-button
        type="primary"
        size="large"
        block
        @click="openSidePanel"
      >
        <template #icon>
          <AppstoreOutlined />
        </template>
        打开管理面板
      </a-button>

      <!-- 快速操作 -->
      <a-row :gutter="8">
        <a-col :span="12">
          <a-button
            block
            @click="openSidePanel"
          >
            <template #icon>
              <ShopOutlined />
            </template>
            上品中心
          </a-button>
        </a-col>
        <a-col :span="12">
          <a-button
            block
            @click="openSidePanel"
          >
            <template #icon>
              <UserOutlined />
            </template>
            子账号
          </a-button>
        </a-col>
      </a-row>

      <!-- 设置入口 -->
      <a-divider />

      <a-button
        block
        @click="openOptionsPage"
      >
        <template #icon>
          <SettingOutlined />
        </template>
        扩展设置
      </a-button>

      <!-- 状态信息 -->
      <a-card size="small" class="mt-4">
        <div class="space-y-2 text-xs">
          <div class="flex justify-between items-center">
            <span class="text-gray-600">店铺状态</span>
            <a-tag color="success" size="small">
              <template #icon>
                <CheckCircleOutlined />
              </template>
              正常
            </a-tag>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-600">积分余额</span>
            <a-tag color="blue" size="small">30</a-tag>
          </div>
        </div>
      </a-card>
    </div>
  </div>
</template>
