{"compilerOptions": {"forceConsistentCasingInFileNames": true, "strict": true, "incremental": true, "composite": true, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "noEmit": true, "baseUrl": "./", "paths": {"@/*": ["./src/*"], "~/*": ["./*"], "src/*": ["./src/*"], "@assets/*": ["./src/assets/*"]}, "types": ["node", "chrome-types", "@intlify/unplugin-vue-i18n/messages"]}, "include": ["package.json", "vite.config.ts", "vite.firefox.config.ts", "manifest.config.ts", "manifest.firefox.config.ts"]}