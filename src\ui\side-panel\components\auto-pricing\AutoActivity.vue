<script setup lang="ts">
import { ref, computed } from 'vue'
import { PlayCircleOutlined } from '@ant-design/icons-vue'
import { getSiteOptions } from '../../../../config/temuSites'

// 自动报名活动表单
const activityForm = ref({
  shop: '',
  site: '',
  skcFilter: '',
  priceSource: '货盘价格方式',
  stockThreshold: '',
  minProfitMargin: '',
  maxProfitMargin: '',
  activityStock: '',
  activityLink: '',
  timeInterval: 3
})

// 店铺选项
const shopOptions = [
  {
    value: 'ZenithCai',
    label: 'ZenithCai',
    avatar: 'https://img.cdnfe.com/supplier-public-tag/201365d418b/060d9c4f-71ff-4da0-8b98-c6c8a8221896_300x300.jpeg'
  }
]

// 站点选项
const siteOptions = computed(() => getSiteOptions().map(site => ({
  value: site.label,
  label: `${site.label}站`
})))

// 价格来源选项
const priceSourceOptions = [
  { value: '货盘价格方式', label: '货盘价格方式' },
  { value: '固定价格方式', label: '固定价格方式' }
]

// 开始报活动
const startActivityRegistration = () => {
  console.log('开始报活动:', activityForm.value)
  alert('自动报名活动功能已启动！')
}
</script>

<template>
  <div>
    <a-form
      :model="activityForm"
      layout="vertical"
      @finish="startActivityRegistration"
      class="space-y-6"
    >
      <!-- 基础配置 -->
      <a-card title="基础配置" class="mb-6">
        <a-row :gutter="16">
          <!-- 店铺选择 -->
          <a-col :span="8">
            <a-form-item label="店铺">
              <a-select
                v-model:value="activityForm.shop"
                placeholder="选择店铺"
              >
                <a-select-option
                  v-for="shop in shopOptions"
                  :key="shop.value"
                  :value="shop.value"
                >
                  <div class="flex items-center space-x-2">
                    <img
                      :src="shop.avatar"
                      alt="店铺头像"
                      class="w-4 h-4 rounded"
                    />
                    <span>{{ shop.label }}</span>
                  </div>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 站点选择 -->
          <a-col :span="8">
            <a-form-item label="站点">
              <a-select
                v-model:value="activityForm.site"
                placeholder="选择站点"
              >
                <a-select-option
                  v-for="site in siteOptions"
                  :key="site.value"
                  :value="site.value"
                >
                  {{ site.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 价格来源 -->
          <a-col :span="8">
            <a-form-item label="价格来源">
              <a-select
                v-model:value="activityForm.priceSource"
                placeholder="选择价格来源"
              >
                <a-select-option
                  v-for="source in priceSourceOptions"
                  :key="source.value"
                  :value="source.value"
                >
                  {{ source.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- SKC筛选 -->
        <a-form-item label="SKC筛选">
          <a-input
            v-model:value="activityForm.skcFilter"
            placeholder="多个查询请英文逗号、空格依次输入(选填)"
          />
        </a-form-item>
      </a-card>

      <!-- 报名条件 -->
      <a-card title="报名条件" class="mb-6">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="库存阈值">
              <a-input-number
                v-model:value="activityForm.stockThreshold"
                placeholder="输入库存阈值"
                class="w-full"
              />
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="最小毛利率(%)">
              <a-input-number
                v-model:value="activityForm.minProfitMargin"
                placeholder="输入最小毛利率"
                class="w-full"
              />
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="最大毛利率(%)">
              <a-input-number
                v-model:value="activityForm.maxProfitMargin"
                placeholder="输入最大毛利率"
                class="w-full"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="活动库存">
              <a-input-number
                v-model:value="activityForm.activityStock"
                placeholder="输入活动库存"
                class="w-full"
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="时间间隔(秒)">
              <a-input-number
                v-model:value="activityForm.timeInterval"
                :min="1"
                class="w-full"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 活动链接 -->
        <a-form-item label="活动链接">
          <a-input
            v-model:value="activityForm.activityLink"
            placeholder="输入活动链接(选填)"
          />
        </a-form-item>
      </a-card>

      <!-- 报名说明 -->
      <a-card title="报名说明" class="mb-6">
        <a-alert
          type="info"
          show-icon
          message="自动报名规则"
          description="系统将根据设置的条件自动为符合要求的商品报名活动，包括库存、毛利率等条件检查。"
        />
        
        <div class="mt-4 space-y-2 text-sm text-gray-600">
          <div>• 系统会定期检查符合条件的商品</div>
          <div>• 自动计算毛利率并判断是否符合报名条件</div>
          <div>• 根据设置的时间间隔执行报名操作</div>
        </div>
      </a-card>

      <!-- 提交按钮 -->
      <div class="flex justify-end">
        <a-button 
          type="primary"
          html-type="submit"
          size="large"
        >
          <template #icon>
            <PlayCircleOutlined />
          </template>
          开始自动报名
        </a-button>
      </div>
    </a-form>
  </div>
</template>
