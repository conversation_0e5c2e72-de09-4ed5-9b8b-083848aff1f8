import configStorageService from './configStorageService'
import type { BasicConfig, ProductConfig } from './configStorageService'

// Amazon产品数据接口
export interface AmazonProductData {
  asin: string
  title: string
  brand?: string
  price?: number
  currency?: string
  rating?: number
  reviewCount?: number
  mainImageUrl?: string
  imageUrls: string[]
  bulletPoints: string[]
  description?: string
  categoryPath?: string
  stockStatus?: string
  specifications?: Record<string, string>
  variations?: AmazonVariation[]
  sourceUrl: string
}

// Amazon变体数据接口
export interface AmazonVariation {
  asin: string
  parentAsin: string
  price?: number
  imageUrl?: string
  stockStatus?: string
  attributes: Record<string, string>
}

// 店小秘格式的产品数据接口
export interface DianxiaomiProductData {
  attributes: string
  categoryId: string
  shopId: string
  productSemiManagedReq: string
  sourceUrl: string
  fullCid: string
  productName: string
  productNameI18n: string
  outerGoodsUrl: string
  materialImgUrl: string
  productOrigin: string
  region2Id: string
  originFileUrl: string
  sensitiveAttr: string
  personalizationSwitch: number
  mainImage: string
  dxmVideoId: string
  optionValue: string
  mainProductSkuSpecReqs: string
  goodsModel: string
  variationListStr: string
  productWarehouseRouteReq: string
  dxmPdfUrl: string
  qualifiedEn: string
  instructionsId: string
  instructionsName: string
  description: string
  instructionsTranslateId: string
  freightTemplateId: string
  shipmentLimitSecond: number
  op: number
  id: string
  categoryType: number
  dxmState: string
  productId: string
  sizeTemplateIds: string
}

class AmazonDataService {
  /**
   * 从Amazon页面提取产品数据
   */
  async extractProductDataFromPage(): Promise<AmazonProductData | null> {
    try {
      console.info('[AmazonDataService] 开始从当前页面提取Amazon产品数据...')
      
      // 检查是否为Amazon产品页面
      if (!this.isAmazonProductPage()) {
        throw new Error('当前页面不是Amazon产品页面')
      }

      const productData: AmazonProductData = {
        asin: this.extractAsin(),
        title: this.extractTitle(),
        brand: this.extractBrand(),
        price: this.extractPrice(),
        currency: 'USD',
        rating: this.extractRating(),
        reviewCount: this.extractReviewCount(),
        mainImageUrl: this.extractMainImage(),
        imageUrls: this.extractImageUrls(),
        bulletPoints: this.extractBulletPoints(),
        description: this.extractDescription(),
        categoryPath: this.extractCategoryPath(),
        stockStatus: this.extractStockStatus(),
        specifications: this.extractSpecifications(),
        variations: this.extractVariations(),
        sourceUrl: window.location.href
      }

      console.info('[AmazonDataService] Amazon产品数据提取完成:', productData)
      return productData
    } catch (error) {
      console.error('[AmazonDataService] 提取Amazon产品数据失败:', error)
      return null
    }
  }

  /**
   * 将Amazon数据转换为店小秘格式
   */
  async convertToDianxiaomiFormat(amazonData: AmazonProductData): Promise<DianxiaomiProductData> {
    try {
      console.info('[AmazonDataService] 开始转换Amazon数据为店小秘格式...')
      
      // 获取配置
      const { basic, product } = await configStorageService.getFullConfig()
      
      // 生成唯一ID
      const uniqueId = Date.now().toString()
      
      // 构建店小秘格式数据
      const dianxiaomiData: DianxiaomiProductData = {
        attributes: this.buildAttributes(amazonData.specifications || {}),
        categoryId: basic.productCategory || "9938",
        shopId: basic.shopAccount || "",
        productSemiManagedReq: "100",
        sourceUrl: amazonData.sourceUrl,
        fullCid: "4547939-",
        productName: this.buildProductName(amazonData.title, product.titlePrefix, product.titleSuffix),
        productNameI18n: JSON.stringify({ en: amazonData.title }),
        outerGoodsUrl: amazonData.sourceUrl,
        materialImgUrl: amazonData.mainImageUrl || "",
        productOrigin: "CN",
        region2Id: basic.businessSite || "**************",
        originFileUrl: "",
        sensitiveAttr: "",
        personalizationSwitch: 0,
        mainImage: amazonData.imageUrls.join('|'),
        dxmVideoId: "0",
        optionValue: "[]",
        mainProductSkuSpecReqs: this.buildMainProductSkuSpecReqs(amazonData),
        goodsModel: "",
        variationListStr: this.buildVariationListStr(amazonData, product, basic),
        productWarehouseRouteReq: this.buildProductWarehouseRouteReq(basic.warehouse, basic.businessSite),
        dxmPdfUrl: "",
        qualifiedEn: "",
        instructionsId: "",
        instructionsName: "",
        description: this.buildDescription(amazonData),
        instructionsTranslateId: "",
        freightTemplateId: basic.freightTemplate || "",
        shipmentLimitSecond: parseInt(basic.shippingTime) || 172800,
        op: 1,
        id: uniqueId,
        categoryType: 0,
        dxmState: basic.publishStatus === "2" ? "online" : "offline",
        productId: "0",
        sizeTemplateIds: ""
      }

      console.info('[AmazonDataService] 数据转换完成:', dianxiaomiData)
      return dianxiaomiData
    } catch (error) {
      console.error('[AmazonDataService] 转换数据格式失败:', error)
      throw error
    }
  }

  /**
   * 模拟推送数据到店小秘
   */
  async simulatePushToDianxiaomi(dianxiaomiData: DianxiaomiProductData): Promise<void> {
    try {
      console.info('[AmazonDataService] 模拟推送数据到店小秘...')
      
      // 压缩数据
      const compressedData = JSON.stringify(dianxiaomiData)
      
      console.info('[AmazonDataService] 准备推送的数据大小:', compressedData.length, '字符')
      console.info('[AmazonDataService] 推送数据预览:', {
        productName: dianxiaomiData.productName,
        sourceUrl: dianxiaomiData.sourceUrl,
        shopId: dianxiaomiData.shopId,
        categoryId: dianxiaomiData.categoryId,
        mainImage: dianxiaomiData.mainImage?.substring(0, 100) + '...'
      })
      
      // 这里可以添加实际的API调用
      // const response = await fetch('店小秘API端点', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: compressedData
      // })
      
      console.info('[AmazonDataService] 数据推送模拟完成')
    } catch (error) {
      console.error('[AmazonDataService] 推送数据失败:', error)
      throw error
    }
  }

  // ==================== 私有方法 ====================

  private isAmazonProductPage(): boolean {
    return window.location.href.includes('amazon.com') && 
           (window.location.href.includes('/dp/') || window.location.href.includes('/gp/product/'))
  }

  private extractAsin(): string {
    const url = window.location.href
    const match = url.match(/\/dp\/([A-Z0-9]{10})/)
    return match ? match[1] : 'UNKNOWN'
  }

  private extractTitle(): string {
    const titleElement = document.querySelector('#productTitle')
    return titleElement?.textContent?.trim() || ''
  }

  private extractBrand(): string | undefined {
    const brandElement = document.querySelector('#bylineInfo')
    if (brandElement) {
      const brandText = brandElement.textContent?.trim() || ''
      const brandMatch = brandText.match(/Visit the (.+?) Store/)
      return brandMatch ? brandMatch[1] : brandText
    }
    return undefined
  }

  private extractPrice(): number | undefined {
    const priceElement = document.querySelector('.a-price .a-offscreen')
    if (priceElement) {
      const priceText = priceElement.textContent?.trim() || ''
      const priceMatch = priceText.replace('$', '').match(/[\d,]+\.?\d*/)
      if (priceMatch) {
        return parseFloat(priceMatch[0].replace(',', ''))
      }
    }
    return undefined
  }

  private extractRating(): number | undefined {
    const ratingElement = document.querySelector('[data-hook="average-star-rating"] .a-icon-alt')
    if (ratingElement) {
      const ratingText = ratingElement.textContent?.trim() || ''
      const ratingMatch = ratingText.match(/(\d+\.?\d*)/)
      return ratingMatch ? parseFloat(ratingMatch[1]) : undefined
    }
    return undefined
  }

  private extractReviewCount(): number | undefined {
    const reviewElement = document.querySelector('[data-hook="total-review-count"]')
    if (reviewElement) {
      const reviewText = reviewElement.textContent?.trim() || ''
      const reviewMatch = reviewText.replace(/,/g, '').match(/(\d+)/)
      return reviewMatch ? parseInt(reviewMatch[1]) : undefined
    }
    return undefined
  }

  private extractMainImage(): string | undefined {
    const mainImageElement = document.querySelector('#landingImage, #imgBlkFront') as HTMLImageElement
    return mainImageElement?.src || mainImageElement?.getAttribute('data-src') || undefined
  }

  private extractImageUrls(): string[] {
    const imageUrls: string[] = []
    const imageElements = document.querySelectorAll('#altImages img, .imageThumbnail img') as NodeListOf<HTMLImageElement>

    imageElements.forEach(img => {
      const imgUrl = img.src || img.getAttribute('data-src')
      if (imgUrl && imgUrl.includes('amazon.com') && !imgUrl.includes('data:image')) {
        imageUrls.push(imgUrl)
      }
    })

    return imageUrls.slice(0, 10) // 限制最多10张图片
  }

  private extractBulletPoints(): string[] {
    const bulletPoints: string[] = []
    const bulletElements = document.querySelectorAll('#featurebullets_feature_div li span.a-list-item')

    bulletElements.forEach(bullet => {
      const text = bullet.textContent?.trim() || ''
      if (text && text.length > 10 && !text.includes('Make sure')) {
        bulletPoints.push(text)
      }
    })

    return bulletPoints.slice(0, 5) // 限制最多5个要点
  }

  private extractDescription(): string | undefined {
    const descElement = document.querySelector('#feature-bullets ul, #featurebullets_feature_div')
    return descElement?.textContent?.trim().substring(0, 1000) || undefined
  }

  private extractCategoryPath(): string | undefined {
    const breadcrumbElements = document.querySelectorAll('#wayfinding-breadcrumbs_feature_div a')
    const categoryPath: string[] = []

    breadcrumbElements.forEach(breadcrumb => {
      const text = breadcrumb.textContent?.trim() || ''
      if (text && text !== 'Home' && text !== 'Amazon.com') {
        categoryPath.push(text)
      }
    })

    return categoryPath.join(' > ') || undefined
  }

  private extractStockStatus(): string | undefined {
    const availabilityElement = document.querySelector('#availability span, #availabilityInsideBuyBox_feature_div span')
    return availabilityElement?.textContent?.trim() || 'In Stock'
  }

  private extractSpecifications(): Record<string, string> {
    const specs: Record<string, string> = {}
    const specTables = document.querySelectorAll('#productDetails_techSpec_section_1 tr, #productDetails_detailBullets_sections1 tr')

    specTables.forEach(row => {
      const cells = row.querySelectorAll('td')
      if (cells.length >= 2) {
        const key = cells[0].textContent?.trim() || ''
        const value = cells[1].textContent?.trim() || ''
        if (key && value) {
          specs[key] = value
        }
      }
    })

    return specs
  }

  private extractVariations(): AmazonVariation[] {
    // 这里可以实现变体提取逻辑
    // 暂时返回空数组
    return []
  }

  private buildAttributes(specifications: Record<string, string>): string {
    const attributes = Object.entries(specifications).map(([key, value], index) => ({
      propName: key,
      refPid: 4000 + index,
      pid: 1700 + index,
      templatePid: 1200000 + index,
      numberInputValue: "",
      valueUnit: "",
      vid: (67000 + index).toString(),
      propValue: value
    }))

    return JSON.stringify(attributes)
  }

  private buildProductName(title: string, prefix: string, suffix: string): string {
    let productName = title
    if (prefix) productName = prefix + ' ' + productName
    if (suffix) productName = productName + ' ' + suffix
    return productName.substring(0, 200) // 限制长度
  }

  private buildMainProductSkuSpecReqs(amazonData: AmazonProductData): string {
    return JSON.stringify([{
      parentSpecId: 0,
      parentSpecName: "",
      specId: 0,
      specName: "",
      previewImgUrls: amazonData.mainImageUrl || "",
      extCode: `${amazonData.asin}[am]1`,
      productSkcId: ""
    }])
  }

  private buildVariationListStr(amazonData: AmazonProductData, productConfig: ProductConfig, basicConfig: BasicConfig): string {
    const variation = {
      id: Date.now().toString(),
      productSkuId: 0,
      supplierPrice: Math.round((amazonData.price || 10) * productConfig.priceMultiplier * 100), // 转换为分
      extCode: `${amazonData.asin}[am]1`,
      length: productConfig.defaultSize.length,
      width: productConfig.defaultSize.width,
      height: productConfig.defaultSize.height,
      weight: productConfig.defaultSize.weight,
      codeType: "1",
      code: "",
      suggestedPrice: Math.round((amazonData.price || 10) * productConfig.priceMultiplier * 100),
      suggestedPriceCurrencyType: "CNY",
      numberOfPieces: 1,
      skuClassification: "1",
      pieceUnitCode: "1",
      individuallyPacked: null,
      thumbUrl: amazonData.mainImageUrl || "",
      productSkuSpecReqs: JSON.stringify([{
        specId: "0",
        specName: "Default",
        parentSpecId: 1001,
        parentSpecName: "颜色"
      }]),
      productSkuStockQuantityReq: JSON.stringify([{
        warehouseId: basicConfig.warehouse,
        targetStockAvailable: productConfig.fixedStock?.toString() || "200"
      }]),
      sitePriceInfo: null
    }

    return JSON.stringify([variation])
  }

  private buildProductWarehouseRouteReq(warehouseId: string, siteId: string): string {
    return JSON.stringify([{
      warehouseId: warehouseId,
      warehouseName: "Amazon仓库",
      siteIdList: [siteId || "100"]
    }])
  }

  private buildDescription(amazonData: AmazonProductData): string {
    const description = []
    
    // 添加文字描述
    if (amazonData.bulletPoints.length > 0) {
      description.push({
        lang: "zh",
        type: "text",
        priority: "0",
        contentList: [{
          text: amazonData.bulletPoints.join(' '),
          textModuleDetails: {
            fontFamily: null,
            fontColor: "#000000",
            backgroundColor: "#ffffff",
            fontSize: "12",
            align: "left"
          }
        }]
      })
    }

    // 添加图片
    amazonData.imageUrls.forEach((imageUrl, index) => {
      description.push({
        lang: "zh",
        type: "image",
        priority: (index + 1).toString(),
        contentList: [{
          imgUrl: imageUrl,
          height: 800,
          width: 800
        }]
      })
    })

    return JSON.stringify(description)
  }
}

// 导出单例实例
export const amazonDataService = new AmazonDataService()
export default amazonDataService
