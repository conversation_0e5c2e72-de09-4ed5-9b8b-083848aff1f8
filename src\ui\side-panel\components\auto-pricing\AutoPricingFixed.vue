<script setup lang="ts">
import { ref, computed } from 'vue'
import { PlayCircleOutlined } from '@ant-design/icons-vue'
import { getSiteOptions } from '../../../../config/temuSites'

// 自动核价固定表单
const fixedPricingForm = ref({
  shop: '',
  site: '',
  repeatCount: 1,
  skcFilter: '',
  pricingCondition: '申报价比例',
  firstPricePercentage: 90,
  fixedAmount: '',
  exampleCost: 100,
  exampleFirstPrice: 150,
  maxPricingAttempts: 4,
  priceReduction: 1,
  abandonPricing: 1
})

// 店铺选项
const shopOptions = [
  {
    value: 'ZenithCai',
    label: 'ZenithCai',
    avatar: 'https://img.cdnfe.com/supplier-public-tag/201365d418b/060d9c4f-71ff-4da0-8b98-c6c8a8221896_300x300.jpeg'
  }
]

// 站点选项
const siteOptions = computed(() => getSiteOptions().map(site => ({
  value: site.label,
  label: `${site.label}站`
})))

// 核价条件选项
const pricingConditionOptions = [
  { value: '申报价比例', label: '申报价比例' },
  { value: '固定金额', label: '固定金额' }
]

// 开始自动核价固定
const startFixedPricing = () => {
  console.log('开始自动核价固定:', fixedPricingForm.value)
  alert('自动核价固定功能已启动！')
}
</script>

<template>
  <div>
    <a-form
      :model="fixedPricingForm"
      layout="vertical"
      @finish="startFixedPricing"
      class="space-y-6"
    >
      <!-- 基础配置 -->
      <a-card title="基础配置" class="mb-6">
        <a-row :gutter="16">
          <!-- 店铺选择 -->
          <a-col :span="8">
            <a-form-item label="店铺">
              <a-select
                v-model:value="fixedPricingForm.shop"
                placeholder="选择店铺"
              >
                <a-select-option
                  v-for="shop in shopOptions"
                  :key="shop.value"
                  :value="shop.value"
                >
                  <div class="flex items-center space-x-2">
                    <img
                      :src="shop.avatar"
                      alt="店铺头像"
                      class="w-4 h-4 rounded"
                    />
                    <span>{{ shop.label }}</span>
                  </div>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 站点选择 -->
          <a-col :span="8">
            <a-form-item label="站点">
              <a-select
                v-model:value="fixedPricingForm.site"
                placeholder="选择站点"
              >
                <a-select-option
                  v-for="site in siteOptions"
                  :key="site.value"
                  :value="site.value"
                >
                  {{ site.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 重复执行次数 -->
          <a-col :span="8">
            <a-form-item label="重复执行次数">
              <a-input-number
                v-model:value="fixedPricingForm.repeatCount"
                :min="1"
                class="w-full"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- SKC筛选 -->
        <a-form-item label="SKC筛选">
          <a-input
            v-model:value="fixedPricingForm.skcFilter"
            placeholder="多个查询请英文逗号、空格依次输入(选填)"
          />
        </a-form-item>
      </a-card>

      <!-- 核价条件 -->
      <a-card title="核价条件" class="mb-6">
        <a-form-item label="核价条件">
          <a-select
            v-model:value="fixedPricingForm.pricingCondition"
            placeholder="选择核价条件"
          >
            <a-select-option
              v-for="condition in pricingConditionOptions"
              :key="condition.value"
              :value="condition.value"
            >
              {{ condition.label }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-row :gutter="16" v-if="fixedPricingForm.pricingCondition === '申报价比例'">
          <a-col :span="12">
            <a-form-item label="首次报价比例(%)">
              <a-input-number
                v-model:value="fixedPricingForm.firstPricePercentage"
                :min="1"
                :max="100"
                class="w-full"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16" v-if="fixedPricingForm.pricingCondition === '固定金额'">
          <a-col :span="12">
            <a-form-item label="固定金额($)">
              <a-input-number
                v-model:value="fixedPricingForm.fixedAmount"
                :min="0"
                :step="0.01"
                class="w-full"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>

      <!-- 示例计算 -->
      <a-card title="示例计算" class="mb-6">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="示例成本($)">
              <a-input-number
                v-model:value="fixedPricingForm.exampleCost"
                :min="0"
                :step="0.01"
                class="w-full"
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="示例首次报价($)">
              <a-input-number
                v-model:value="fixedPricingForm.exampleFirstPrice"
                :min="0"
                :step="0.01"
                class="w-full"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>

      <!-- 重新报价条件 -->
      <a-card title="重新报价条件" class="mb-6">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="最大核价次数">
              <a-input-number
                v-model:value="fixedPricingForm.maxPricingAttempts"
                :min="1"
                addon-after="次"
                class="w-full"
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="每次降价($)">
              <a-input-number
                v-model:value="fixedPricingForm.priceReduction"
                :min="0.1"
                :step="0.1"
                class="w-full"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="放弃核价条件">
          <a-radio-group v-model:value="fixedPricingForm.abandonPricing">
            <a-radio :value="1">拒绝，放弃上新</a-radio>
            <a-radio :value="2">不处理</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-card>

      <!-- 核价说明 -->
      <a-card title="核价说明" class="mb-6">
        <a-alert
          type="info"
          show-icon
          message="固定核价规则"
          description="使用固定的核价策略，不依赖货盘成本，适用于有固定定价策略的商品。"
        />
        
        <div class="mt-4 space-y-2 text-sm text-gray-600">
          <div>• 申报价比例：基于申报价的百分比进行核价</div>
          <div>• 固定金额：使用固定的金额进行核价</div>
          <div>• 免费功能：此功能为免费提供</div>
        </div>
      </a-card>

      <!-- 提交按钮 -->
      <div class="flex justify-end">
        <a-button 
          type="primary"
          html-type="submit"
          size="large"
        >
          <template #icon>
            <PlayCircleOutlined />
          </template>
          开始固定核价
        </a-button>
      </div>
    </a-form>
  </div>
</template>
