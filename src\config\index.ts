// 应用配置管理
// 统一管理所有配置参数，支持环境变量和默认值

interface AppConfig {
  // API 配置
  api: {
    baseUrl: string
    timeout: number
    retryCount: number
  }
  
  // Temu 配置
  temu: {
    sellerDomains: string[]
    apiUrl: string
    retryInterval: number
  }
  
  // 店小秘配置
  dianxiaomi: {
    baseUrl: string
    apiVersion: string
    timeout: number
  }
  
  // 应用配置
  app: {
    name: string
    version: string
    debug: boolean
  }
  
  // 存储配置
  storage: {
    prefix: string
    encryptionKey?: string
  }
}

// 默认配置
const defaultConfig: AppConfig = {
  api: {
    baseUrl: 'http://127.0.0.1:32000/api',
    timeout: 30000, // 30秒
    retryCount: 3
  },
  
  temu: {
    sellerDomains: [
      'seller.temu.com',
      'seller.kuajingmaihuo.com',
      'seller-cn.temu.com',
      'agentseller.temu.com',
      'agentseller-us.temu.com'
    ],
    apiUrl: 'https://seller.kuajingmaihuo.com/bg/quiet/api/mms/userInfo',
    retryInterval: 5000 // 5秒
  },
  
  dianxiaomi: {
    baseUrl: 'https://api.dianxiaomi.com',
    apiVersion: 'v1',
    timeout: 15000 // 15秒
  },
  
  app: {
    name: '胡建大卖家',
    version: '1.0.0',
    debug: false
  },
  
  storage: {
    prefix: 'hjdmj_',
    encryptionKey: undefined
  }
}

// 从环境变量获取配置
const getEnvConfig = (): Partial<AppConfig> => {
  const envConfig: Partial<AppConfig> = {}
  
  // API 配置
  if (import.meta.env.VITE_API_BASE_URL) {
    envConfig.api = {
      ...defaultConfig.api,
      baseUrl: import.meta.env.VITE_API_BASE_URL
    }
  }
  
  if (import.meta.env.VITE_API_TIMEOUT) {
    envConfig.api = {
      ...envConfig.api || defaultConfig.api,
      timeout: parseInt(import.meta.env.VITE_API_TIMEOUT)
    }
  }
  
  // 店小秘配置
  if (import.meta.env.VITE_DIANXIAOMI_BASE_URL) {
    envConfig.dianxiaomi = {
      ...defaultConfig.dianxiaomi,
      baseUrl: import.meta.env.VITE_DIANXIAOMI_BASE_URL
    }
  }
  
  // 应用配置
  if (import.meta.env.DEV !== undefined) {
    envConfig.app = {
      ...defaultConfig.app,
      debug: import.meta.env.DEV
    }
  }
  
  if (import.meta.env.VITE_APP_VERSION) {
    envConfig.app = {
      ...envConfig.app || defaultConfig.app,
      version: import.meta.env.VITE_APP_VERSION
    }
  }
  
  return envConfig
}

// 合并配置
const mergeConfig = (base: AppConfig, override: Partial<AppConfig>): AppConfig => {
  const result = { ...base }
  
  Object.keys(override).forEach(key => {
    const typedKey = key as keyof AppConfig
    if (override[typedKey] && typeof override[typedKey] === 'object') {
      result[typedKey] = {
        ...base[typedKey],
        ...override[typedKey]
      } as any
    } else if (override[typedKey] !== undefined) {
      result[typedKey] = override[typedKey] as any
    }
  })
  
  return result
}

// 最终配置
export const config: AppConfig = mergeConfig(defaultConfig, getEnvConfig())

// 配置工具函数
export const getApiUrl = (path: string): string => {
  const baseUrl = config.api.baseUrl.replace(/\/$/, '') // 移除末尾斜杠
  const cleanPath = path.replace(/^\//, '') // 移除开头斜杠
  return `${baseUrl}/${cleanPath}`
}

export const getTemuApiUrl = (): string => {
  return config.temu.apiUrl
}

export const getDianxiaomiApiUrl = (path: string): string => {
  const baseUrl = config.dianxiaomi.baseUrl.replace(/\/$/, '')
  const cleanPath = path.replace(/^\//, '')
  return `${baseUrl}/${config.dianxiaomi.apiVersion}/${cleanPath}`
}

export const isDebugMode = (): boolean => {
  return config.app.debug
}

export const getStorageKey = (key: string): string => {
  return `${config.storage.prefix}${key}`
}

// 动态更新配置（用于运行时配置更改）
export const updateConfig = (updates: Partial<AppConfig>): void => {
  Object.assign(config, mergeConfig(config, updates))
  console.info('[Config] 配置已更新:', updates)
}

// 导出配置类型
export type { AppConfig }

// 打印当前配置（仅在调试模式下）
if (isDebugMode()) {
  console.info('[Config] 当前配置:', config)
}
