// 店小秘检测服务
// 检测店小秘登录状态并获取相关数据

import { config } from '../config'

// 店小秘登录状态
interface DianxiaomiLoginStatus {
  isLoggedIn: boolean
  message: string
  shopCount?: number
  userInfo?: DianxiaomiUserInfo
  error?: string
}

// 店小秘用户信息
interface DianxiaomiUserInfo {
  id: number
  account: string
  accountAlias?: string
  puid: number
  vipLevel: number
  vipExpired: number
  state: number
  wechatBind: boolean
  shopMap: Record<string, DianxiaomiShop>
}

// 店小秘店铺信息
interface DianxiaomiShop {
  id: number
  puid: number
  name: string
  platform: string
  merchantId?: string
  awsAccessKeyId?: string // 用作币种
  isExpire?: number
  authTime?: string
  createTime?: string
  updateTime?: string
  idStr: string
}

// 店铺账号信息（简化版，用于UI显示）
interface ShopAccount {
  shopId: string
  shopName: string
  currency: string
  platform: string
  merchantId?: string
  isExpire?: number
  authTime?: string
  createTime?: string
  updateTime?: string
}

// 发货仓库信息
interface Warehouse {
  warehouseId: string
  warehouseName: string
  shopId: string
  siteId: string
}

// 运费模板信息
interface FreightTemplate {
  id: number
  puid: number
  shopId: number
  site: number
  freightTemplateId: string
  templateName: string
  createTime: number
  updateTime: number
}

// 商品分类信息
interface ProductCategory {
  id: number
  catId: number
  catName: string
  parentCatId: number
  catLevel: number
  isLeaf: boolean
}

class DianxiaomiDetectionService {
  private readonly STORAGE_KEY = 'dianxiaomi_session'

  // 检测店小秘登录状态 - 使用用户信息API
  async checkLoginStatus(): Promise<DianxiaomiLoginStatus> {
    try {
      console.info('[DianxiaomiDetection] 通过用户信息API检测登录状态...')

      // 调用用户信息API
      const response = await fetch('https://www.dianxiaomi.com/api/userInfo.json', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'X-Requested-With': 'XMLHttpRequest'
        }
      })

      console.info('[DianxiaomiDetection] 用户信息API响应状态:', response.status)

      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          return {
            isLoggedIn: false,
            message: '未登录店小秘，请先登录'
          }
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      console.info('[DianxiaomiDetection] 用户信息API响应数据:', result)

      if (result.code !== 0) {
        return {
          isLoggedIn: false,
          message: result.msg || '获取用户信息失败'
        }
      }

      const userInfo: DianxiaomiUserInfo = result.data

      // 过滤出Temu店铺（platform为pddkj）
      const temuShops = Object.values(userInfo.shopMap).filter(shop => shop.platform === 'pddkj')
      const shopCount = temuShops.length

      console.info('[DianxiaomiDetection] 用户信息分析:')
      console.info('- 用户ID:', userInfo.id)
      console.info('- 用户账号:', userInfo.account)
      console.info('- 总店铺数:', Object.keys(userInfo.shopMap).length)
      console.info('- Temu店铺数:', shopCount)
      console.info('- Temu店铺列表:', temuShops.map(shop => ({ id: shop.id, name: shop.name })))

      // 保存用户信息到session
      await this.saveUserSession(userInfo, temuShops)

      if (shopCount > 0) {
        return {
          isLoggedIn: true,
          message: `已登录店小秘，检测到 ${shopCount} 个Temu店铺`,
          shopCount: shopCount,
          userInfo: userInfo
        }
      } else {
        return {
          isLoggedIn: true,
          message: '已登录店小秘，但没有Temu店铺',
          shopCount: 0,
          userInfo: userInfo
        }
      }

    } catch (error) {
      console.error('[DianxiaomiDetection] 登录状态检测失败:', error)
      return {
        isLoggedIn: false,
        message: `登录状态检测失败: ${error instanceof Error ? error.message : '未知错误'}`,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  // 保存用户会话信息
  private async saveUserSession(userInfo: DianxiaomiUserInfo, temuShops: DianxiaomiShop[]): Promise<void> {
    try {
      const sessionData = {
        userInfo: {
          id: userInfo.id,
          account: userInfo.account,
          puid: userInfo.puid,
          vipLevel: userInfo.vipLevel
        },
        temuShops: temuShops.map(shop => ({
          shopId: shop.idStr,
          shopName: shop.name,
          currency: shop.awsAccessKeyId || 'CNY',
          platform: shop.platform,
          merchantId: shop.merchantId,
          isExpire: shop.isExpire
        })),
        selectedShopId: temuShops.length > 0 ? temuShops[0].idStr : null,
        timestamp: Date.now()
      }

      await chrome.storage.session.set({ [this.STORAGE_KEY]: sessionData })
      console.info('[DianxiaomiDetection] 用户会话信息已保存:', sessionData)
    } catch (error) {
      console.error('[DianxiaomiDetection] 保存用户会话失败:', error)
    }
  }

  // 获取用户会话信息
  async getUserSession(): Promise<{
    userInfo: {
      id: number
      account: string
      puid: number
      vipLevel: number
    }
    temuShops: ShopAccount[]
    selectedShopId: string | null
    timestamp: number
  } | null> {
    try {
      const result = await chrome.storage.session.get(this.STORAGE_KEY)
      return result[this.STORAGE_KEY] || null
    } catch (error) {
      console.error('[DianxiaomiDetection] 获取用户会话失败:', error)
      return null
    }
  }

  // 设置选中的店铺ID
  async setSelectedShopId(shopId: string): Promise<void> {
    try {
      const session = await this.getUserSession()
      if (session) {
        session.selectedShopId = shopId
        session.timestamp = Date.now()
        await chrome.storage.session.set({ [this.STORAGE_KEY]: session })
        console.info('[DianxiaomiDetection] 已设置选中店铺ID:', shopId)
      }
    } catch (error) {
      console.error('[DianxiaomiDetection] 设置选中店铺ID失败:', error)
    }
  }

  // 获取选中的店铺ID
  async getSelectedShopId(): Promise<string | null> {
    try {
      const session = await this.getUserSession()
      return session?.selectedShopId || null
    } catch (error) {
      console.error('[DianxiaomiDetection] 获取选中店铺ID失败:', error)
      return null
    }
  }

  // 获取店铺账号列表 - 优先使用session数据，必要时重新获取
  async getShopAccounts(): Promise<{ success: boolean; data?: ShopAccount[]; error?: string }> {
    try {
      console.info('[DianxiaomiDetection] 获取店铺账号列表...')

      // 先尝试从session获取
      const session = await this.getUserSession()
      if (session && session.temuShops && session.temuShops.length > 0) {
        // 检查session是否过期（1小时）
        const isExpired = Date.now() - session.timestamp > 60 * 60 * 1000
        if (!isExpired) {
          console.info('[DianxiaomiDetection] 从session获取店铺数据:', session.temuShops)
          return {
            success: true,
            data: session.temuShops
          }
        } else {
          console.info('[DianxiaomiDetection] Session已过期，重新获取用户信息')
        }
      }

      // Session无效或过期，重新检测登录状态获取最新数据
      console.info('[DianxiaomiDetection] Session无效，重新获取用户信息...')
      const loginStatus = await this.checkLoginStatus()

      if (!loginStatus.isLoggedIn) {
        return {
          success: false,
          error: loginStatus.message
        }
      }

      // 从最新的session获取店铺数据
      const newSession = await this.getUserSession()
      if (newSession && newSession.temuShops) {
        console.info('[DianxiaomiDetection] 获取到最新店铺数据:', newSession.temuShops)
        return {
          success: true,
          data: newSession.temuShops
        }
      }

      return {
        success: false,
        error: '无法获取店铺信息'
      }
    } catch (error) {
      console.error('[DianxiaomiDetection] 获取店铺账号失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取店铺信息失败'
      }
    }
  }

  // 获取发货仓库列表 - 使用正确的API（可能也需要POST请求）
  async getWarehouses(): Promise<{ success: boolean; data?: Record<string, Record<string, Record<string, string>>>; error?: string }> {
    try {
      console.info('[DianxiaomiDetection] 获取发货仓库列表...')

      // 先尝试GET请求
      let response = await fetch('https://www.dianxiaomi.com/popTemuCategory/syncAllWarehouseList.json', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'X-Requested-With': 'XMLHttpRequest'
        }
      })

      console.info('[DianxiaomiDetection] 仓库API (GET) 响应状态:', response.status)

      // 如果GET请求失败，尝试POST请求
      if (!response.ok) {
        console.info('[DianxiaomiDetection] GET请求失败，尝试POST请求...')

        // 获取店铺信息以获取shopId
        const shopResult = await this.getShopAccounts()
        if (!shopResult.success || !shopResult.data || shopResult.data.length === 0) {
          return {
            success: false,
            error: '无法获取店铺ID，请先同步店铺账号'
          }
        }

        const shopId = shopResult.data[0].shopId
        console.info('[DianxiaomiDetection] 使用店铺ID:', shopId)

        // 构建POST请求参数
        const formData = new URLSearchParams()
        formData.append('shopId', shopId)
        formData.append('siteIdStr', '100') // 默认使用美国站

        response = await fetch('https://www.dianxiaomi.com/popTemuCategory/syncAllWarehouseList.json', {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Accept': '*/*',
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
          },
          body: formData.toString()
        })

        console.info('[DianxiaomiDetection] 仓库API (POST) 响应状态:', response.status)
      }

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      console.info('[DianxiaomiDetection] 仓库API响应数据:', result)

      if (result.code === 0) {
        return {
          success: true,
          data: result.data
        }
      } else {
        return {
          success: false,
          error: result.msg || '获取仓库列表失败'
        }
      }
    } catch (error) {
      console.error('[DianxiaomiDetection] 获取仓库列表失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '请求失败'
      }
    }
  }

  // 获取运费模板列表 - 使用正确的API（需要POST请求）
  async getFreightTemplates(): Promise<{ success: boolean; data?: FreightTemplate[]; error?: string }> {
    try {
      console.info('[DianxiaomiDetection] 获取运费模板列表...')

      // 首先获取店铺信息以获取shopId
      const shopResult = await this.getShopAccounts()
      if (!shopResult.success || !shopResult.data || shopResult.data.length === 0) {
        return {
          success: false,
          error: '无法获取店铺ID，请先同步店铺账号'
        }
      }

      const shopId = shopResult.data[0].shopId
      console.info('[DianxiaomiDetection] 使用店铺ID:', shopId)

      // 构建POST请求参数
      const formData = new URLSearchParams()
      formData.append('shopId', shopId)
      formData.append('siteIdStr', '100') // 默认使用美国站

      const response = await fetch('https://www.dianxiaomi.com/popTemuCategory/syncTemuShipments.json', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Accept': '*/*',
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData.toString()
      })

      console.info('[DianxiaomiDetection] 运费模板API响应状态:', response.status)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      console.info('[DianxiaomiDetection] 运费模板API响应数据:', result)

      if (result.code === 0) {
        return {
          success: true,
          data: result.data
        }
      } else {
        return {
          success: false,
          error: result.msg || '获取运费模板失败'
        }
      }
    } catch (error) {
      console.error('[DianxiaomiDetection] 获取运费模板失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '请求失败'
      }
    }
  }

  // 获取商品分类列表 - 直接AJAX请求
  async getProductCategories(parentId?: number): Promise<{ success: boolean; data?: ProductCategory[]; error?: string }> {
    try {
      console.info('[DianxiaomiDetection] 直接获取商品分类列表...', { parentId })

      // 首先获取店铺信息以获取shopId
      const shopResult = await this.getShopAccounts()
      if (!shopResult.success || !shopResult.data || shopResult.data.length === 0) {
        return {
          success: false,
          error: '无法获取店铺ID，请先同步店铺账号'
        }
      }

      const shopId = shopResult.data[0].shopId
      console.info('[DianxiaomiDetection] 使用店铺ID:', shopId)

      // 构建POST请求参数
      const formData = new URLSearchParams()
      formData.append('shopId', shopId)
      formData.append('categoryParentId', (parentId || 0).toString())

      const response = await fetch('https://www.dianxiaomi.com/api/popTemuCategory/list.json', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Accept': '*/*',
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData.toString()
      })

      console.info('[DianxiaomiDetection] 商品分类API响应状态:', response.status)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      console.info('[DianxiaomiDetection] 商品分类API响应数据:', data)

      if (data.code === 0) {
        return {
          success: true,
          data: data.data
        }
      } else {
        return {
          success: false,
          error: data.msg || '获取商品分类失败'
        }
      }
    } catch (error) {
      console.error('[DianxiaomiDetection] 获取商品分类失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '请求失败'
      }
    }
  }






  // 获取指定店铺的商品分类列表
  async getTemuCategories(shopId: string, parentId?: number): Promise<{ success: boolean; data?: ProductCategory[]; error?: string }> {
    try {
      console.info('[DianxiaomiDetection] 获取Temu商品分类列表...', { shopId, parentId })

      // 构建POST请求参数
      const formData = new URLSearchParams()
      formData.append('shopId', shopId)
      if (parentId) {
        formData.append('categoryParentId', parentId.toString())
      }

      const response = await fetch('https://www.dianxiaomi.com/api/popTemuCategory/list.json', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Accept': '*/*',
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData.toString()
      })

      console.info('[DianxiaomiDetection] Temu分类API响应状态:', response.status)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      console.info('[DianxiaomiDetection] Temu分类API响应数据:', data)

      if (data.code === 0) {
        return {
          success: true,
          data: data.data
        }
      } else {
        return {
          success: false,
          error: data.msg || '获取商品分类失败'
        }
      }
    } catch (error) {
      console.error('[DianxiaomiDetection] 获取Temu商品分类失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '请求失败'
      }
    }
  }

}

// 创建单例实例
export const dianxiaomiDetectionService = new DianxiaomiDetectionService()
export default dianxiaomiDetectionService

// 导出类型
export type {
  DianxiaomiLoginStatus,
  ShopAccount,
  Warehouse,
  FreightTemplate,
  ProductCategory
}
