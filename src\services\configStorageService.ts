import { useBrowserLocalStorage } from '../composables/useBrowserStorage'

// 基础设置配置类型
export interface BasicConfig {
  erpPlatform: string
  publishSite: string
  shopAccount: string
  publishStatus: string
  businessSite: string
  warehouse: string
  freightTemplate: string
  shippingTime: string
  venue: string
  productCategory: string
  productAttributes: string
}

// 上品配置类型
export interface ProductConfig {
  minStock: number
  fixedStock: number | null
  enableDeduplication: boolean
  titlePrefix: string
  titleSuffix: string
  uploadInterval: number
  priceMultiplier: number
  collectDetails: string[]
  collectSku: boolean
  externalLink: boolean
  defaultSize: {
    length: number
    width: number
    height: number
    weight: number
  }
  filterProhibited: boolean
  prohibitedWords: string
  enableTranslation: boolean
  translationService: string
}

// 默认基础配置
const defaultBasicConfig: BasicConfig = {
  erpPlatform: '店小秘',
  publishSite: '',
  shopAccount: '',
  publishStatus: '2',
  businessSite: '',
  warehouse: '',
  freightTemplate: '',
  shippingTime: '86400',
  venue: '',
  productCategory: '',
  productAttributes: ''
}

// 默认上品配置
const defaultProductConfig: ProductConfig = {
  minStock: 12,
  fixedStock: null,
  enableDeduplication: true,
  titlePrefix: '',
  titleSuffix: '',
  uploadInterval: 0,
  priceMultiplier: 4,
  collectDetails: ['title', 'img'],
  collectSku: true,
  externalLink: false,
  defaultSize: {
    length: 10,
    width: 10,
    height: 10,
    weight: 20
  },
  filterProhibited: true,
  prohibitedWords: '',
  enableTranslation: false,
  translationService: 'google'
}

class ConfigStorageService {
  private basicConfigStorage = useBrowserLocalStorage('temu-extension-basic-config', defaultBasicConfig)
  private productConfigStorage = useBrowserLocalStorage('temu-extension-product-config', defaultProductConfig)

  /**
   * 保存基础设置配置
   */
  async saveBasicConfig(config: BasicConfig): Promise<void> {
    try {
      this.basicConfigStorage.data.value = { ...config }
      console.info('[ConfigStorage] 基础设置配置已保存:', config)
    } catch (error) {
      console.error('[ConfigStorage] 保存基础设置配置失败:', error)
      throw error
    }
  }

  /**
   * 获取基础设置配置
   */
  async getBasicConfig(): Promise<BasicConfig> {
    try {
      await this.basicConfigStorage.promise
      const config = { ...this.basicConfigStorage.data.value }
      console.info('[ConfigStorage] 获取基础设置配置:', config)
      return config
    } catch (error) {
      console.error('[ConfigStorage] 获取基础设置配置失败:', error)
      return { ...defaultBasicConfig }
    }
  }

  /**
   * 保存上品配置
   */
  async saveProductConfig(config: ProductConfig): Promise<void> {
    try {
      this.productConfigStorage.data.value = { ...config }
      console.info('[ConfigStorage] 上品配置已保存:', config)
    } catch (error) {
      console.error('[ConfigStorage] 保存上品配置失败:', error)
      throw error
    }
  }

  /**
   * 获取上品配置
   */
  async getProductConfig(): Promise<ProductConfig> {
    try {
      await this.productConfigStorage.promise
      const config = { ...this.productConfigStorage.data.value }
      console.info('[ConfigStorage] 获取上品配置:', config)
      return config
    } catch (error) {
      console.error('[ConfigStorage] 获取上品配置失败:', error)
      return { ...defaultProductConfig }
    }
  }

  /**
   * 重置基础设置配置为默认值
   */
  async resetBasicConfig(): Promise<void> {
    try {
      this.basicConfigStorage.data.value = { ...defaultBasicConfig }
      console.info('[ConfigStorage] 基础设置配置已重置为默认值')
    } catch (error) {
      console.error('[ConfigStorage] 重置基础设置配置失败:', error)
      throw error
    }
  }

  /**
   * 重置上品配置为默认值
   */
  async resetProductConfig(): Promise<void> {
    try {
      this.productConfigStorage.data.value = { ...defaultProductConfig }
      console.info('[ConfigStorage] 上品配置已重置为默认值')
    } catch (error) {
      console.error('[ConfigStorage] 重置上品配置失败:', error)
      throw error
    }
  }

  /**
   * 获取完整配置（用于Amazon数据采集时调用）
   */
  async getFullConfig(): Promise<{ basic: BasicConfig; product: ProductConfig }> {
    try {
      const [basic, product] = await Promise.all([
        this.getBasicConfig(),
        this.getProductConfig()
      ])
      
      const fullConfig = { basic, product }
      console.info('[ConfigStorage] 获取完整配置:', fullConfig)
      return fullConfig
    } catch (error) {
      console.error('[ConfigStorage] 获取完整配置失败:', error)
      throw error
    }
  }

  /**
   * 检查配置是否完整（用于验证是否可以进行数据采集）
   */
  async isConfigComplete(): Promise<{ isComplete: boolean; missingFields: string[] }> {
    try {
      const { basic, product } = await this.getFullConfig()
      const missingFields: string[] = []

      // 检查基础设置必填字段
      if (!basic.shopAccount) missingFields.push('店铺账号')
      if (!basic.businessSite) missingFields.push('经营站点')
      if (!basic.warehouse) missingFields.push('发货仓库')
      if (!basic.freightTemplate) missingFields.push('运费模板')
      if (!basic.productCategory) missingFields.push('商品分类')

      // 检查上品配置必填字段
      if (!product.minStock || product.minStock <= 0) missingFields.push('最小库存')
      if (!product.priceMultiplier || product.priceMultiplier <= 0) missingFields.push('价格倍数')

      const isComplete = missingFields.length === 0
      
      console.info('[ConfigStorage] 配置完整性检查:', { isComplete, missingFields })
      return { isComplete, missingFields }
    } catch (error) {
      console.error('[ConfigStorage] 配置完整性检查失败:', error)
      return { isComplete: false, missingFields: ['配置检查失败'] }
    }
  }
}

// 导出单例实例
export const configStorageService = new ConfigStorageService()
export default configStorageService
