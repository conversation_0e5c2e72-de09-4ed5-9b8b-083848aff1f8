<script setup lang="ts">
import { ref, computed, onMounted } from "vue"
import { ReloadOutlined } from '@ant-design/icons-vue'
import AutoPricingModal from "./auto-pricing.vue"
import TemuNotFound from "../../../components/TemuNotFound.vue"
import { useDashboard } from "../../../composables/useDashboard"
import { useShopBinding } from "../../../composables/useShopBinding"
import { useAppRouter, AppPageState } from "../../../composables/useAppRouter"
import { getSiteOptions } from '../../../config/temuSites'

// 使用应用路由管理 - 仅用于工作台页面的 Temu 绑定检查
const {
  currentPageState,
  isLoading: appLoading,
  temuLoginStatus,
  backendBindingStatus,
  checkAppState,
  handleBindingSuccess,
  handleLoginSuccess,
  getStateDescription
} = useAppRouter()

// 使用 Dashboard 数据管理
const {
  isLoading,
  todoData,
  products,
  tabs,
  activeTab,
  currentPage,
  pageSize,
  total,
  searchForm,
  currentShopInfo,
  mallId,
  refreshData,
  searchProducts,
  switchTab,
  handlePageChange,
  jumpToSource,
} = useDashboard()

// 处理绑定成功事件
const onBindingSuccess = () => {
  handleBindingSuccess()
}

// 处理登录成功事件
const onLoginSuccess = () => {
  handleLoginSuccess()
}

// 组件挂载时检测应用状态
onMounted(() => {
  checkAppState()
})

// 使用店铺绑定状态
const { shopBinding } = useShopBinding()

// 数据源切换
const dataSource = ref(true) // true: 本地24小时内数据, false: 货盘

// 站点选项 - 使用统一的站点配置
const siteOptions = computed(() => getSiteOptions().map(site => ({
  value: site.code.toLowerCase(),
  label: `${site.label}站`
})))

// 商品ID类型选项
const idTypeOptions = [
  { value: "SKC", label: "SKC" },
  { value: "SPU", label: "SPU" },
  { value: "SKU", label: "SKU" },
]

// 当前店铺信息（从绑定状态获取）
const currentShop = computed(() => {
  if (currentShopInfo.value) {
    return {
      label: currentShopInfo.value.shopName || currentShopInfo.value.mallName || "Unknown Shop",
      avatar: currentShopInfo.value.logo || "",
      shopId: currentShopInfo.value.mallId?.toString() || currentShopInfo.value.shopId?.toString() || "",
      memberType: "会员",
      site: "美国站",
      url: `https://www.temu.com/mall.html?mall_id=${currentShopInfo.value.mallId || currentShopInfo.value.shopId}`,
    }
  }
  return {
    label: "未绑定店铺",
    avatar: "",
    shopId: "",
    memberType: "",
    site: "",
    url: "",
  }
})

// 功能函数
const openMerchantBackend = () => {
  if (currentShop.value?.url) {
    window.open(currentShop.value.url, "_blank")
  }
}

const autoSyncStock = () => {
  console.info("自动同步库存...")
}

const autoFollowPrice = () => {
  console.info("自动跟价管理...")
}

const autoActivityRegistration = () => {
  console.info("自动活动报名...")
}

// 显示自动核价弹窗
const showAutoPricingModal = ref(false)

const openAutoPricingModal = () => {
  showAutoPricingModal.value = true
}

const closeAutoPricingModal = () => {
  showAutoPricingModal.value = false
}



// 表格列定义
const tableColumns = [
  {
    title: '📦 产品信息',
    key: 'product',
    width: 400,
    fixed: 'left'
  },
  {
    title: '💰 申报信息',
    key: 'declared',
    width: 280
  },
  {
    title: '🎨 SKU属性',
    key: 'sku',
    width: 320
  },
  {
    title: '📅 时间信息',
    key: 'time',
    width: 180,
    align: 'center'
  }
]
</script>

<template>
  <div class="h-full overflow-y-auto bg-gray-50">
    <!-- 加载状态 -->
    <div v-if="currentPageState === AppPageState.LOADING" class="h-full flex items-center justify-center">
      <div class="text-center">
        <div class="inline-flex items-center">
          <svg class="animate-spin -ml-1 mr-3 h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span class="text-lg text-gray-700">{{ getStateDescription() }}</span>
        </div>
        <div class="mt-4 text-sm text-gray-500">
          正在检测 Temu 登录状态和后台绑定状态...
        </div>
      </div>
    </div>

    <!-- Temu 未登录状态 -->
    <div v-else-if="currentPageState === AppPageState.TEMU_NOT_FOUND" class="h-full">
      <TemuNotFound
        @login-success="onLoginSuccess"
      />
    </div>

    <!-- 需要绑定状态 -->
    <div v-else-if="currentPageState === AppPageState.BINDING_REQUIRED" class="h-full">
      <TemuNotFound
        :show-binding-mode="true"
        :temu-user-info="temuLoginStatus.userInfo"
        :temu-shop-info="temuLoginStatus.shopInfo"
        @binding-success="onBindingSuccess"
      />
    </div>

    <!-- 工作台主内容 - 只有在 DASHBOARD 状态下才显示 -->
    <div v-else-if="currentPageState === AppPageState.DASHBOARD" class="h-full overflow-y-auto bg-gray-50">
    <!-- 店铺信息卡片 -->
    <a-card
      class="m-4 shadow-sm"
      :bordered="false"
    >
      <div class="flex items-center justify-between">
        <!-- 左侧店铺信息 -->
        <div class="flex items-center space-x-6">
          <!-- 平台标识 -->
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gradient-to-r from-orange-400 to-red-500 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-lg">T</span>
            </div>
            <div>
              <h2 class="text-xl font-semibold text-gray-800 mb-1">Temu 店铺管理</h2>
              <p class="text-sm text-gray-500">跨境电商智能管理平台</p>
            </div>
          </div>

          <!-- 店铺信息 -->
          <div class="flex items-center space-x-4 pl-6 border-l border-gray-200">
            <a-avatar
              v-if="currentShop.avatar"
              :src="currentShop.avatar"
              :size="32"
            />
            <div>
              <div class="flex items-center space-x-2 mb-1">
                <span class="font-medium text-gray-800">{{ currentShop.label }}</span>
                <a-tag
                  v-if="currentShop.memberType"
                  color="blue"
                  size="small"
                >
                  {{ currentShop.memberType }}
                </a-tag>
              </div>
              <p class="text-xs text-gray-500">店铺ID: {{ currentShop.shopId || '未设置' }}</p>
            </div>
          </div>
        </div>

        <!-- 右侧操作区域 -->
        <div class="flex items-center space-x-4">
          <!-- 待办事项统计 -->
          <div class="text-center px-4 py-2 bg-red-50 rounded-lg">
            <div class="text-2xl font-bold text-red-600">{{ todoData.total || 0 }}</div>
            <div class="text-xs text-red-500">待办事项</div>
          </div>

          <!-- 数据源切换 -->
          <div class="flex items-center space-x-2">
            <span class="text-sm text-gray-600">数据来源:</span>
            <a-switch
              v-model:checked="dataSource"
              size="small"
              checked-children="本地"
              un-checked-children="货盘"
            />
          </div>

          <!-- 操作按钮 -->
          <div class="flex items-center space-x-2">
            <a-button
              type="text"
              :loading="isLoading"
              @click="refreshData"
              class="flex items-center"
            >
              <ReloadOutlined v-if="!isLoading" />
              刷新
            </a-button>
            <a-button
              type="primary"
              @click="openMerchantBackend"
              class="bg-blue-600 hover:bg-blue-700"
            >
              打开商家后台
            </a-button>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 商品管理区域 -->
    <a-card
      class="mx-4 mb-4 shadow-sm"
      :bordered="false"
    >
      <!-- 标签页导航 -->
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center space-x-1">
          <h3 class="text-lg font-semibold text-gray-800 mr-4">商品状态</h3>
          <div class="flex flex-wrap gap-2">
            <a-button
              v-for="tab in tabs"
              :key="tab.key"
              :type="activeTab === tab.key ? 'primary' : 'default'"
              size="default"
              class="relative"
              @click="switchTab(tab.key)"
            >
              {{ tab.label }}
              <span
                v-if="tab.count > 0"
                class="ml-2 px-2 py-0.5 text-xs rounded-full"
                :class="activeTab === tab.key
                  ? 'bg-white text-blue-600'
                  : 'bg-blue-100 text-blue-600'"
              >
                {{ tab.count }}
              </span>
            </a-button>
          </div>
        </div>

        <!-- 功能按钮组 -->
        <div class="flex items-center space-x-3">
          <a-button
            type="default"
            class="bg-green-50 border-green-200 text-green-700 hover:bg-green-100 font-medium"
            @click="autoSyncStock"
          >
            📦 同步库存
          </a-button>
          <a-button
            type="default"
            class="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100 font-medium"
            @click="openAutoPricingModal"
          >
            💰 自动核价
          </a-button>
          <a-button
            type="default"
            class="bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100 font-medium"
            @click="autoFollowPrice"
          >
            📈 跟价管理
          </a-button>
          <a-button
            type="default"
            class="bg-orange-50 border-orange-200 text-orange-700 hover:bg-orange-100 font-medium"
            @click="autoActivityRegistration"
          >
            🎯 活动报名
          </a-button>
        </div>
      </div>

      <!-- 搜索表单 -->
      <div class="border-t border-gray-100 pt-6">
        <h4 class="text-md font-medium text-gray-700 mb-4">🔍 商品搜索</h4>
        <a-form
          :model="searchForm"
          layout="vertical"
          @finish="searchProducts"
          class="bg-gray-50 p-4 rounded-lg"
        >
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="站点" class="mb-3">
                <a-select
                  v-model:value="searchForm.site"
                  placeholder="选择站点"
                  size="small"
                >
                  <a-select-option
                    v-for="site in siteOptions"
                    :key="site.value"
                    :value="site.value"
                  >
                    {{ site.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>

            <a-col :span="8">
              <a-form-item label="商品ID查询" class="mb-3">
                <a-input-group compact>
                  <a-select
                    v-model:value="searchForm.productIdType"
                    style="width: 30%"
                    size="small"
                  >
                    <a-select-option
                      v-for="type in idTypeOptions"
                      :key="type.value"
                      :value="type.value"
                    >
                      {{ type.label }}
                    </a-select-option>
                  </a-select>
                  <a-input
                    v-model:value="searchForm.productId"
                    placeholder="多个ID用逗号分隔"
                    style="width: 70%"
                    size="small"
                  />
                </a-input-group>
              </a-form-item>
            </a-col>

            <a-col :span="7">
              <a-form-item label="货号" class="mb-3">
                <a-input-group compact>
                  <a-select
                    v-model:value="searchForm.skuType"
                    style="width: 30%"
                    size="small"
                  >
                    <a-select-option
                      v-for="type in idTypeOptions"
                      :key="type.value"
                      :value="type.value"
                    >
                      {{ type.label }}
                    </a-select-option>
                  </a-select>
                  <a-input
                    v-model:value="searchForm.sku"
                    placeholder="输入货号"
                    style="width: 70%"
                    size="small"
                  />
                </a-input-group>
              </a-form-item>
            </a-col>

            <a-col :span="3">
              <a-form-item label=" " class="mb-3">
                <a-button
                  type="primary"
                  html-type="submit"
                  block
                  size="small"
                  class="bg-blue-600 hover:bg-blue-700"
                >
                  🔍 查询
                </a-button>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>

    <!-- 商品列表 -->
    <a-card
      class="mx-4 mb-4 shadow-sm"
      :bordered="false"
    >
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-800">📦 商品列表</h3>
        <div class="text-sm text-gray-500">
          共 {{ total }} 件商品
        </div>
      </div>

      <a-spin
        :spinning="isLoading"
        tip="正在加载商品数据..."
      >
        <!-- 商品表格 -->
        <a-table
          v-if="products.length > 0"
          :columns="tableColumns"
          :data-source="products"
          :pagination="{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: handlePageChange,
            onShowSizeChange: (current, size) => { pageSize = size; handlePageChange(current); },
            size: 'small'
          }"
          :scroll="{ x: 1200 }"
          row-key="id"
          size="small"
          bordered
          class="custom-table"
        >
          <!-- 产品信息列 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'product'">
              <div class="flex items-start space-x-3 p-2">
                <div class="relative">
                  <a-image
                    :src="record.image"
                    :alt="record.title"
                    :width="72"
                    :height="72"
                    :preview="true"
                    class="rounded-lg shadow-sm border border-gray-200"
                  />
                  <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                </div>
                <div class="flex-1 min-w-0">
                  <div class="mb-2">
                    <a-typography-text
                      strong
                      :ellipsis="{ rows: 2, tooltip: record.title }"
                      class="text-gray-900 text-sm leading-5 hover:text-blue-600 cursor-pointer"
                    >
                      {{ record.title }}
                    </a-typography-text>
                  </div>
                  <div class="grid grid-cols-2 gap-x-4 gap-y-1 text-xs">
                    <div class="flex items-center space-x-1">
                      <span class="text-gray-400">SPU:</span>
                      <a-tag size="small" color="blue">{{ record.spu }}</a-tag>
                    </div>
                    <div class="flex items-center space-x-1">
                      <span class="text-gray-400">SKC:</span>
                      <a-tag size="small" color="green">{{ record.skc }}</a-tag>
                    </div>
                    <div class="flex items-center space-x-1">
                      <span class="text-gray-400">站点:</span>
                      <a-tag size="small" color="orange">{{ record.site }}</a-tag>
                    </div>
                    <div class="flex items-center space-x-1">
                      <span class="text-gray-400">币种:</span>
                      <span class="text-gray-600 font-medium">{{ record.currency }}</span>
                    </div>
                  </div>
                  <div class="mt-2">
                    <a-typography-text
                      type="secondary"
                      class="text-xs text-gray-500 line-clamp-1"
                      :title="record.category"
                    >
                      📂 {{ record.category }}
                    </a-typography-text>
                  </div>
                </div>
              </div>
            </template>

            <template v-else-if="column.key === 'declared'">
              <div class="p-3 bg-gray-50 rounded-lg">
                <div class="space-y-3">
                  <div class="flex flex-col">
                    <span class="text-xs text-gray-500 mb-1">💰 申报价格</span>
                    <div class="flex items-center space-x-2">
                      <a-tag
                        color="red"
                        size="default"
                        class="font-semibold"
                      >
                        {{ record.declaredPrice }}
                      </a-tag>
                    </div>
                  </div>
                  <div class="flex flex-col">
                    <span class="text-xs text-gray-500 mb-1">📅 创建时间</span>
                    <span class="text-sm text-gray-700 font-medium">
                      {{ record.createTime }}
                    </span>
                  </div>
                </div>
              </div>
            </template>

            <template v-else-if="column.key === 'sku'">
              <div class="flex items-start space-x-3 p-3">
                <div class="relative">
                  <a-image
                    :src="record.sku.image"
                    :alt="record.sku.color"
                    :width="48"
                    :height="48"
                    :preview="true"
                    class="rounded-lg border border-gray-200"
                  />
                </div>
                <div class="flex-1 space-y-2">
                  <div class="flex items-center space-x-2">
                    <span class="text-xs text-gray-500">🎨</span>
                    <a-tag
                      color="purple"
                      size="small"
                      class="font-medium"
                    >
                      {{ record.sku.color }}
                    </a-tag>
                  </div>

                  <div class="space-y-1">
                    <div class="flex items-center space-x-2">
                      <span class="text-xs text-gray-400">货号:</span>
                      <span class="text-xs text-gray-700 font-mono">{{ record.sku.itemNo }}</span>
                    </div>

                    <div class="flex items-center space-x-2">
                      <span class="text-xs text-gray-400">状态:</span>
                      <a-tag
                        :color="record.sku.status === '价格申报中' ? 'processing' : 'default'"
                        size="small"
                      >
                        {{ record.sku.status }}
                      </a-tag>
                    </div>

                    <div class="flex items-center space-x-2">
                      <span class="text-xs text-gray-400">价格:</span>
                      <span class="text-sm font-bold text-red-600">{{ record.sku.price }}</span>
                    </div>
                  </div>

                  <a-button
                    type="link"
                    size="small"
                    class="p-0 h-auto text-xs text-blue-600 hover:text-blue-800"
                    @click="jumpToSource(record)"
                  >
                    🔗 跳转货源
                  </a-button>
                </div>
              </div>
            </template>

            <template v-else-if="column.key === 'time'">
              <div class="text-center p-3">
                <div class="flex flex-col items-center space-y-2">
                  <div class="text-xs text-gray-500">📅 创建时间</div>
                  <div class="text-sm text-gray-800 font-medium">
                    {{ record.createTime }}
                  </div>
                  <a-tag
                    color="blue"
                    size="small"
                    class="text-xs"
                  >
                    新品
                  </a-tag>
                </div>
              </div>
            </template>
          </template>
        </a-table>

        <!-- 空数据状态 -->
        <a-empty
          v-else
          description="暂无商品数据"
        >
          <a-button
            type="primary"
            @click="refreshData"
          >
            刷新数据
          </a-button>
        </a-empty>
      </a-spin>
    </a-card>



    <!-- 数据统计卡片 -->
    <div class="grid grid-cols-4 gap-4 mx-4 mb-4">
      <!-- 商品总数 -->
      <a-card
        class="text-center shadow-sm border-l-4 border-l-blue-500"
        :bordered="false"
      >
        <div class="text-2xl font-bold text-blue-600 mb-1">{{ total }}</div>
        <div class="text-sm text-gray-600">商品总数</div>
      </a-card>

      <!-- 待办事项 -->
      <a-card
        class="text-center shadow-sm border-l-4 border-l-red-500"
        :bordered="false"
      >
        <div class="text-2xl font-bold text-red-600 mb-1">{{ todoData.total || 0 }}</div>
        <div class="text-sm text-gray-600">待办事项</div>
      </a-card>

      <!-- 当前标签页 -->
      <a-card
        class="text-center shadow-sm border-l-4 border-l-green-500"
        :bordered="false"
      >
        <div class="text-lg font-semibold text-green-600 mb-1">
          {{ tabs.find(tab => tab.key === activeTab)?.label || '全部' }}
        </div>
        <div class="text-sm text-gray-600">当前分类</div>
      </a-card>

      <!-- 连接状态 -->
      <a-card
        class="text-center shadow-sm border-l-4"
        :class="currentShop.shopId ? 'border-l-green-500' : 'border-l-gray-400'"
        :bordered="false"
      >
        <div class="text-lg font-semibold mb-1"
             :class="currentShop.shopId ? 'text-green-600' : 'text-gray-500'">
          {{ currentShop.shopId ? '已连接' : '未连接' }}
        </div>
        <div class="text-sm text-gray-600">店铺状态</div>
      </a-card>
    </div>

      <!-- 快速操作提示 -->
      <a-card>
        <template #title>
          <a-space>
            💡 快速开始
          </a-space>
        </template>
        <a-steps
          direction="vertical"
          size="small"
          :current="0"
        >
          <a-step title="选择店铺平台">
            <template #description>
              首先选择您要管理的店铺平台
            </template>
          </a-step>
          <a-step title="登录店铺账号">
            <template #description>
              点击"打开商家后台"登录您的店铺账号
            </template>
          </a-step>
          <a-step title="设置发布规则">
            <template #description>
              使用"上品中心"设置商品发布规则
            </template>
          </a-step>
          <a-step title="管理商品">
            <template #description>
              在"本土商品管理"中查看和管理您的商品
            </template>
          </a-step>
        </a-steps>
      </a-card>
    </div>

    <!-- 自动核价弹窗 -->
    <AutoPricingModal
      v-if="showAutoPricingModal"
      @close="closeAutoPricingModal"
    />
  </div>
</template>

<style scoped>
/* 全局字体大小调整 */
:deep(.ant-typography) {
  font-size: 14px !important;
}

:deep(.ant-btn) {
  font-size: 14px !important;
  height: auto !important;
  padding: 6px 15px !important;
}

:deep(.ant-form-item-label > label) {
  font-size: 14px !important;
}

:deep(.ant-input) {
  font-size: 14px !important;
}

:deep(.ant-select) {
  font-size: 14px !important;
}

/* 自定义表格样式 */
.custom-table :deep(.ant-table-thead > tr > th) {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  font-weight: 600;
  color: #1e293b;
  font-size: 14px !important;
  padding: 12px 16px !important;
  text-align: center;
}

.custom-table :deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f8fafc;
  transform: scale(1.01);
  transition: all 0.2s ease;
}

.custom-table :deep(.ant-table-tbody > tr > td) {
  border: 1px solid #e2e8f0;
  padding: 0 !important;
  font-size: 13px !important;
  vertical-align: top;
}

.custom-table :deep(.ant-table-container) {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.custom-table :deep(.ant-table) {
  font-size: 13px !important;
}

.custom-table :deep(.ant-table-tbody > tr) {
  transition: all 0.2s ease;
}

/* 产品卡片样式 */
.product-card {
  transition: all 0.2s ease;
}

.product-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 标签样式增强 */
:deep(.ant-tag) {
  border-radius: 6px;
  font-weight: 500;
  border: none;
}

/* 图片样式 */
:deep(.ant-image) {
  transition: all 0.2s ease;
}

:deep(.ant-image:hover) {
  transform: scale(1.05);
}

/* 卡片阴影效果 */
.shadow-sm {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* 响应式网格 */
@media (max-width: 1200px) {
  .grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .grid-cols-4 {
    grid-template-columns: repeat(1, 1fr);
  }
}

/* 按钮悬停效果 */
:deep(.ant-btn:hover) {
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

/* 标签页按钮样式 */
:deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
  font-size: 14px !important;
}

:deep(.ant-btn-primary:hover) {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
}

/* 统计卡片字体 */
.text-2xl {
  font-size: 1.75rem !important;
}

.text-lg {
  font-size: 1.125rem !important;
}

.text-sm {
  font-size: 0.875rem !important;
}

/* 标题字体 */
h2, h3, h4 {
  font-size: 1.125rem !important;
  font-weight: 600 !important;
}
</style>
