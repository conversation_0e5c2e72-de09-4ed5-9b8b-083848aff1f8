<script setup lang="ts">
import { ref, computed, watch, defineProps, defineEmits } from 'vue'
import type { ShopAccount, FreightTemplate, ProductCategory } from '../../../services/dianxiaomiDetectionService'
import CategoryCascader from './CategoryCascader.vue'
import { getSiteOptions, getSiteById } from '../../../config/temuSites'
import configStorageService from '../../../services/configStorageService'
import productConfigService from '../../../services/productConfigService'
import { message } from 'ant-design-vue'

// Props
const props = defineProps<{
  basicForm: {
    erpPlatform: string
    publishSite: string
    shopAccount: string
    publishStatus: string
    businessSite: string
    warehouse: string
    freightTemplate: string
    shippingTime: string
    venue: string
    productCategory: string
    productAttributes: string
  }
  dianxiaomiLoginStatus: {
    isLoggedIn: boolean
    message: string
    loading: boolean
  }
  shopAccounts: ShopAccount[]
  warehouses: Record<string, Record<string, Record<string, string>>>
  freightTemplates: FreightTemplate[]
  productCategories: ProductCategory[]
  loadingStates: {
    shopAccounts: boolean
    warehouses: boolean
    freightTemplates: boolean
    productCategories: boolean
  }
  currentTemuShop: string
}>()

// Emits
const emit = defineEmits<{
  'update:basicForm': [value: typeof props.basicForm]
  'check-login': []
  'open-erp': []
  'load-shop-accounts': []
  'load-warehouses': []
  'load-freight-templates': []
  'load-product-categories': []
  'shop-account-change': [shopId: string]
  'category-change': [category: ProductCategory | null]
  'load-categories-by-parent': [parentId?: number]
  'save-settings': []
}>()

// 站点选项
const siteOptions = computed(() => getSiteOptions())

// 计算属性
const getWarehouseOptions = computed(() => {
  const options: Array<{ value: string; label: string; shopId: string; site: string }> = []
  
  Object.entries(props.warehouses).forEach(([shopId, shopWarehouses]) => {
    Object.entries(shopWarehouses).forEach(([site, siteWarehouses]) => {
      Object.entries(siteWarehouses).forEach(([warehouseId, warehouseName]) => {
        options.push({
          value: warehouseId,
          label: `${warehouseName} (店铺: ${shopId}, 站点: ${site})`,
          shopId,
          site
        })
      })
    })
  })
  
  return options
})

// 检查授权是否即将过期（30天内）
const isExpiringSoon = (expireTime: string): boolean => {
  if (!expireTime) return false
  try {
    const expireDate = new Date(expireTime)
    const now = new Date()
    const diffTime = expireDate.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays <= 30 && diffDays > 0
  } catch {
    return false
  }
}

// 更新表单数据
const updateForm = (key: string, value: any) => {
  const newForm = { ...props.basicForm, [key]: value }
  emit('update:basicForm', newForm)
}

// 事件处理
const handleShopAccountChange = (shopId: string) => {
  updateForm('shopAccount', shopId)
  emit('shop-account-change', shopId)
}

// 级联选择器状态
const selectedCategoryPath = ref<number[]>([])
const selectedCategoryLabels = ref<string[]>([])

const handleCategoryChange = (category: ProductCategory | null) => {
  emit('category-change', category)
}

const handleLoadCategoriesByParent = async (parentId?: number): Promise<ProductCategory[]> => {
  emit('load-categories-by-parent', parentId)
  return []
}

// 级联选择器变化处理
interface CascaderOption {
  value: number
  label: string
  isLeaf?: boolean
  children?: CascaderOption[]
}

const handleCascaderChange = (value: number[], selectedOptions: CascaderOption[]) => {
  selectedCategoryPath.value = value
  selectedCategoryLabels.value = selectedOptions.map(option => option.label)

  // 更新表单中的分类名称（使用最后一级的名称）
  const categoryName = selectedOptions.length > 0
    ? selectedOptions[selectedOptions.length - 1].label
    : ''
  updateForm('productCategory', categoryName)

  // 如果选择了完整的分类路径，显示商品配置按钮
  if (value.length > 0 && selectedOptions.length > 0) {
    const lastCategory = selectedOptions[selectedOptions.length - 1]
    if (lastCategory.isLeaf) {
      // 选择了叶子节点，可以进行商品配置
      console.info('[BasicSettings] 选择了完整分类路径，可以进行商品配置:', {
        categoryId: lastCategory.value,
        categoryName: lastCategory.label,
        fullPath: selectedCategoryLabels.value.join(' / ')
      })
    }
  }

  // 创建一个模拟的 ProductCategory 对象用于向上传递
  if (selectedOptions.length > 0) {
    const lastOption = selectedOptions[selectedOptions.length - 1]
    const mockCategory: ProductCategory = {
      id: lastOption.value,
      catId: lastOption.value,
      catName: lastOption.label,
      parentCatId: selectedOptions.length > 1 ? selectedOptions[selectedOptions.length - 2].value : 0,
      catLevel: selectedOptions.length,
      isLeaf: lastOption.isLeaf || false,
      catType: 0,
      isHidden: false,
      hiddenType: 0,
      classType: -1,
      enabledModelType: -1,
      enabledModel: false,
      needGuideFile: false,
      hasUpdate: 1,
      deleted: 0,
      createTime: Date.now(),
      updateTime: Date.now(),
      siteId: null,
      nodePathId: null,
      nodePath: null,
      classId: null,
      parentClassId: null,
      relatedClassIds: null
    }
    emit('category-change', mockCategory)
  } else {
    emit('category-change', null)
  }
}

const handleSave = () => {
  emit('save-settings')
}

// 测试配置
const testConfiguration = async () => {
  try {
    message.info('开始测试配置...')

    // 检查配置完整性
    const configCheck = await configStorageService.isConfigComplete()

    if (configCheck.isComplete) {
      // 获取完整配置
      const fullConfig = await configStorageService.getFullConfig()

      console.info('[BasicSettings] 配置测试结果:', {
        isComplete: true,
        basicConfig: fullConfig.basic,
        productConfig: fullConfig.product
      })

      message.success('配置测试通过！所有必填项都已配置完成。')
    } else {
      console.warn('[BasicSettings] 配置不完整:', configCheck.missingFields)
      message.warning(`配置不完整，缺少以下项目：${configCheck.missingFields.join(', ')}`)
    }
  } catch (error) {
    console.error('[BasicSettings] 配置测试失败:', error)
    message.error('配置测试失败: ' + (error instanceof Error ? error.message : '未知错误'))
  }
}

// 跳转到店小秘商品配置页面
const openProductConfig = () => {
  if (!props.basicForm.shopAccount) {
    message.error('请先选择店铺账号')
    return
  }

  if (selectedCategoryPath.value.length === 0) {
    message.error('请先选择商品分类')
    return
  }

  const categoryId = selectedCategoryPath.value[selectedCategoryPath.value.length - 1]
  const shopId = props.basicForm.shopAccount

  // 构建店小秘商品配置页面URL
  const configUrl = `https://www.dianxiaomi.com/userTemplate/popTemuAdd.htm?shopId=${shopId}&from=dmo&pushtype=popTemuProduct&categoryId=${categoryId}`

  console.info('[BasicSettings] 打开商品配置页面:', {
    url: configUrl,
    shopId,
    categoryId,
    categoryPath: selectedCategoryLabels.value.join(' / ')
  })

  // 打开新标签页
  window.open(configUrl, '_blank')

  message.info('已打开商品配置页面，请在新页面中完成配置并保存')
}

// 计算属性：是否可以进行商品配置
const canConfigProduct = computed(() => {
  return props.basicForm.shopAccount &&
         selectedCategoryPath.value.length > 0 &&
         props.dianxiaomiLoginStatus.isLoggedIn
})

// 商品配置状态
const productConfigStatus = ref<{
  hasConfig: boolean
  config?: any
  loading: boolean
}>({
  hasConfig: false,
  config: null,
  loading: false
})

// 检查商品配置状态
const checkProductConfigStatus = async () => {
  if (!props.basicForm.shopAccount || selectedCategoryPath.value.length === 0) {
    productConfigStatus.value = { hasConfig: false, config: null, loading: false }
    return
  }

  productConfigStatus.value.loading = true
  try {
    const categoryId = selectedCategoryPath.value[selectedCategoryPath.value.length - 1]
    const config = await productConfigService.getProductConfig(props.basicForm.shopAccount, categoryId)

    productConfigStatus.value = {
      hasConfig: config !== null,
      config: config,
      loading: false
    }

    console.info('[BasicSettings] 商品配置状态检查完成:', productConfigStatus.value)
  } catch (error) {
    console.error('[BasicSettings] 检查商品配置状态失败:', error)
    productConfigStatus.value = { hasConfig: false, config: null, loading: false }
  }
}

// 监听分类选择变化，检查配置状态
watch([() => props.basicForm.shopAccount, selectedCategoryPath], () => {
  checkProductConfigStatus()
}, { immediate: true })
</script>

<template>
  <div class="basic-settings-container">
    <a-card
      title="基础设置"
      class="shadow-sm mx-auto max-w-4xl"
      :bordered="false"
    >
      <template #extra>
        <a-tag color="blue">📋 配置ERP平台、店铺信息等基础设置</a-tag>
      </template>

      <a-form
        :model="basicForm"
        layout="horizontal"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        @finish="handleSave"
        class="space-y-4"
      >
        <!-- ERP平台 -->
        <a-form-item
          label="ERP平台"
          name="erpPlatform"
          :rules="[{ required: true, message: '请选择ERP平台' }]"
        >
          <a-select
            :value="basicForm.erpPlatform"
            @change="(value) => updateForm('erpPlatform', value)"
            placeholder="请选择ERP平台"
          >
            <a-select-option value="店小秘">店小秘</a-select-option>
          </a-select>
        </a-form-item>

        <!-- 发布站点 -->
        <a-form-item label="发布站点">
          <a-input
            :value="currentTemuShop"
            readonly
            class="bg-gray-50"
          >
            <template #addonBefore>
              <a-tag
                :color="currentTemuShop.includes('半托') ? 'blue' : 'green'"
                size="small"
              >
                {{ currentTemuShop.includes('半托') ? '半托' : '全托' }}
              </a-tag>
            </template>
          </a-input>
        </a-form-item>

        <!-- 店小秘登录状态检测 -->
        <a-form-item v-if="!dianxiaomiLoginStatus.isLoggedIn" :wrapper-col="{ span: 24, offset: 0 }">
          <a-alert
            type="error"
            show-icon
            class="mb-4"
          >
            <template #message>
              <div class="flex items-center justify-between">
                <span>{{ dianxiaomiLoginStatus.message }}</span>
                <a-space>
                  <a-button
                    type="primary"
                    size="small"
                    @click="emit('open-erp')"
                  >
                    打开店小秘ERP
                  </a-button>
                  <a-button
                    size="small"
                    :loading="dianxiaomiLoginStatus.loading"
                    @click="emit('check-login')"
                  >
                    {{ dianxiaomiLoginStatus.loading ? '检测中...' : '再次重试' }}
                  </a-button>
                </a-space>
              </div>
            </template>
          </a-alert>
        </a-form-item>

        <!-- 店铺账号 -->
        <a-form-item
          v-if="dianxiaomiLoginStatus.isLoggedIn"
          name="shopAccount"
          :rules="[{ required: true, message: '请选择店铺账号' }]"
        >
          <template #label>
            <div class="flex items-center space-x-2">
              <span>店铺账号</span>
              <a-tag v-if="shopAccounts.length > 0" color="blue" size="small">
                共 {{ shopAccounts.length }} 个
              </a-tag>
            </div>
          </template>

          <a-input-group compact>
            <a-select
              :value="basicForm.shopAccount"
              @change="handleShopAccountChange"
              placeholder="请选择店铺账号"
              :loading="loadingStates.shopAccounts"
              style="width: calc(100% - 80px)"
            >
              <a-select-option
                v-for="shop in shopAccounts"
                :key="shop.shopId"
                :value="shop.shopId"
              >
                {{ shop.shopName }} (ID: {{ shop.shopId }}) - {{ shop.currency }}
              </a-select-option>
            </a-select>
            <a-button
              @click="emit('load-shop-accounts')"
              :loading="loadingStates.shopAccounts"
              style="width: 80px"
            >
              {{ loadingStates.shopAccounts ? '同步中' : '同步' }}
            </a-button>
          </a-input-group>

          <!-- 显示选中店铺的详细信息 -->
          <div v-if="basicForm.shopAccount" class="mt-3">
            <div v-for="shop in shopAccounts" :key="shop.shopId">
              <a-card
                v-if="shop.shopId === basicForm.shopAccount"
                size="small"
                class="bg-blue-50 border-blue-200"
              >
                <template #title>
                  <div class="flex items-center justify-between">
                    <span class="text-blue-900 text-sm">{{ shop.shopName }}</span>
                    <a-tag color="success" size="small">已绑定</a-tag>
                  </div>
                </template>

                <a-descriptions :column="3" size="small">
                  <a-descriptions-item label="店铺ID">
                    <a-typography-text code>{{ shop.shopId }}</a-typography-text>
                  </a-descriptions-item>
                  <a-descriptions-item label="币种">
                    <a-tag color="blue">{{ shop.currency }}</a-tag>
                  </a-descriptions-item>
                  <a-descriptions-item v-if="shop.authTime" label="授权时间">
                    <span class="text-xs">{{ shop.authTime }}</span>
                  </a-descriptions-item>
                  <a-descriptions-item v-if="shop.expireTime" label="过期时间" :span="3">
                    <span :class="isExpiringSoon(shop.expireTime) ? 'text-red-600 font-medium text-xs' : 'text-xs'">
                      {{ shop.expireTime }}
                    </span>
                  </a-descriptions-item>
                </a-descriptions>
              </a-card>
            </div>
          </div>
        </a-form-item>

        <!-- 发布状态 -->
        <a-form-item label="发布状态" name="publishStatus">
          <a-radio-group
            :value="basicForm.publishStatus"
            @change="(e) => updateForm('publishStatus', e.target.value)"
          >
            <a-radio value="2">直接发布</a-radio>
            <a-radio value="3">移入DXM待发布</a-radio>
            <a-radio value="1">放置DXM草稿箱</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 经营站点 -->
        <a-form-item
          label="经营站点"
          name="businessSite"
          :rules="[{ required: true, message: '请选择经营站点' }]"
        >
          <a-select
            :value="basicForm.businessSite"
            @change="(value) => updateForm('businessSite', value)"
            placeholder="请选择经营站点"
          >
            <a-select-option
              v-for="site in siteOptions"
              :key="site.value"
              :value="site.value"
            >
              {{ site.label }} ({{ site.code }})
            </a-select-option>
          </a-select>

          <!-- 显示选中站点的详细信息 -->
          <div v-if="basicForm.businessSite" class="mt-2">
            <div v-if="getSiteById(Number(basicForm.businessSite))" class="text-sm">
              <a-space>
                <a-tag color="blue" size="small">
                  {{ getSiteById(Number(basicForm.businessSite))?.region }}
                </a-tag>
                <span class="text-gray-500 text-xs">
                  货币：{{ getSiteById(Number(basicForm.businessSite))?.currency }}
                </span>
                <span class="text-gray-500 text-xs">
                  语言：{{ getSiteById(Number(basicForm.businessSite))?.language }}
                </span>
              </a-space>
            </div>
          </div>
        </a-form-item>

        <!-- 发货仓库 -->
        <a-form-item
          v-if="dianxiaomiLoginStatus.isLoggedIn"
          label="发货仓库"
          name="warehouse"
          :rules="[{ required: true, message: '请选择发货仓库' }]"
        >
          <a-input-group compact>
            <a-select
              :value="basicForm.warehouse"
              @change="(value) => updateForm('warehouse', value)"
              placeholder="请选择发货仓库"
              :loading="loadingStates.warehouses"
              style="width: calc(100% - 80px)"
            >
              <a-select-option
                v-for="warehouse in getWarehouseOptions"
                :key="warehouse.value"
                :value="warehouse.value"
              >
                {{ warehouse.label }}
              </a-select-option>
            </a-select>
            <a-button
              @click="emit('load-warehouses')"
              :loading="loadingStates.warehouses"
              style="width: 80px"
              size="small"
            >
              {{ loadingStates.warehouses ? '同步中' : '同步' }}
            </a-button>
          </a-input-group>
        </a-form-item>

        <!-- 运费模板 -->
        <a-form-item
          v-if="dianxiaomiLoginStatus.isLoggedIn"
          label="运费模板"
          name="freightTemplate"
          :rules="[{ required: true, message: '请选择运费模板' }]"
        >
          <a-input-group compact>
            <a-select
              :value="basicForm.freightTemplate"
              @change="(value) => updateForm('freightTemplate', value)"
              placeholder="请选择运费模板"
              :loading="loadingStates.freightTemplates"
              style="width: calc(100% - 80px)"
            >
              <a-select-option
                v-for="template in freightTemplates"
                :key="template.id"
                :value="template.freightTemplateId"
              >
                {{ template.templateName }} (店铺: {{ template.shopId }}, 站点: {{ template.site }})
              </a-select-option>
            </a-select>
            <a-button
              @click="emit('load-freight-templates')"
              :loading="loadingStates.freightTemplates"
              style="width: 80px"
              size="small"
            >
              {{ loadingStates.freightTemplates ? '同步中' : '同步' }}
            </a-button>
          </a-input-group>
        </a-form-item>

        <!-- 发货时效 -->
        <a-form-item
          label="发货时效"
          name="shippingTime"
          :rules="[{ required: true, message: '请选择发货时效' }]"
        >
          <a-radio-group
            :value="basicForm.shippingTime"
            @change="(e) => updateForm('shippingTime', e.target.value)"
          >
            <a-radio value="86400">1个工作日内发货</a-radio>
            <a-radio value="172800">2个工作日内发货</a-radio>
            <a-radio value="777600">9个工作日内发货(Y2)</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 商品分类 -->
        <a-form-item
          v-if="dianxiaomiLoginStatus.isLoggedIn"
          label="商品分类"
          name="productCategory"
          :rules="[{ required: true, message: '请选择商品分类' }]"
        >
          <CategoryCascader
            :value="selectedCategoryPath"
            :shop-id="basicForm.shopAccount"
            @change="handleCascaderChange"
            placeholder="请选择商品分类"
          />

          <!-- 显示选中的分类路径 -->
          <div v-if="selectedCategoryPath.length > 0" class="mt-2">
            <a-tag color="blue" size="small">
              {{ selectedCategoryLabels.join(' / ') }}
            </a-tag>
          </div>
        </a-form-item>

        <!-- 商品配置 -->
        <a-form-item label="商品配置">
          <div class="space-y-3">
            <!-- 配置状态显示 -->
            <div v-if="productConfigStatus.loading" class="flex items-center space-x-2">
              <a-spin size="small" />
              <span class="text-sm text-gray-500">检查配置状态中...</span>
            </div>

            <div v-else-if="productConfigStatus.hasConfig" class="bg-green-50 p-3 rounded border-l-4 border-green-400">
              <div class="flex items-center justify-between">
                <div>
                  <div class="font-medium text-green-800 mb-1">✅ 已有商品配置</div>
                  <div class="text-xs text-green-600">
                    配置时间: {{ productConfigStatus.config?.timestamp ? new Date(productConfigStatus.config.timestamp).toLocaleString() : '未知' }}
                  </div>
                  <div class="text-xs text-green-600">
                    分类路径: {{ productConfigStatus.config?.categoryPath || '未知' }}
                  </div>
                </div>
                <a-button
                  type="primary"
                  size="small"
                  @click="openProductConfig"
                  :disabled="!canConfigProduct"
                >
                  🔧 重新配置
                </a-button>
              </div>
            </div>

            <!-- 配置按钮 -->
            <div v-else>
              <a-button
                type="primary"
                @click="openProductConfig"
                :disabled="!canConfigProduct"
                size="default"
                class="mr-2"
              >
                🔧 配置商品属性
              </a-button>
              <a-tooltip v-if="!canConfigProduct" title="请先选择店铺账号和商品分类">
                <a-button disabled size="small">
                  ❓ 需要完成前置配置
                </a-button>
              </a-tooltip>
            </div>

            <!-- 配置说明 -->
            <div class="text-sm text-gray-600 bg-blue-50 p-3 rounded border-l-4 border-blue-400">
              <div class="font-medium text-blue-800 mb-1">📋 配置说明：</div>
              <ul class="list-disc list-inside space-y-1 text-xs">
                <li>选择商品分类后，点击"配置商品属性"按钮</li>
                <li>系统将打开店小秘商品配置页面</li>
                <li>在配置页面中设置商品属性、规格等信息</li>
                <li>点击"保存设置"后，配置将自动保存到插件本地缓存</li>
              </ul>
            </div>
          </div>
        </a-form-item>

        <!-- 保存按钮 -->
        <a-form-item :wrapper-col="{ span: 24, offset: 0 }">
          <div class="flex justify-center pt-4 space-x-4">
            <a-button
              type="primary"
              html-type="submit"
              size="large"
              class="px-12 h-10"
            >
              💾 保存基础设置
            </a-button>
            <a-button
              type="default"
              size="large"
              class="px-8 h-10"
              @click="testConfiguration"
            >
              🧪 测试配置
            </a-button>
          </div>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<style scoped>
.basic-settings-container {
  width: 100%;
  padding: 0 40px;
  margin: 0 auto;
  max-width: 1200px;
}

.basic-settings-container :deep(.ant-form-item-label > label) {
  font-weight: 600;
  font-size: 14px;
}

.basic-settings-container :deep(.ant-form-item) {
  margin-bottom: 20px;
}

.basic-settings-container :deep(.ant-descriptions-item-label) {
  font-weight: 500;
  color: #6b7280;
  font-size: 12px;
}

.basic-settings-container :deep(.ant-descriptions-item-content) {
  font-size: 12px;
}

.basic-settings-container :deep(.ant-card-head-title) {
  font-size: 16px;
  font-weight: 600;
}

.basic-settings-container :deep(.ant-select) {
  font-size: 14px;
}

.basic-settings-container :deep(.ant-input) {
  font-size: 14px;
}

.basic-settings-container :deep(.ant-radio-wrapper) {
  font-size: 14px;
}

/* 紧凑布局优化 */
.basic-settings-container :deep(.ant-form-horizontal .ant-form-item-label) {
  padding-right: 12px;
}

.basic-settings-container :deep(.ant-form-horizontal .ant-form-item-control) {
  flex: 1;
}

/* 卡片内容优化 */
.basic-settings-container :deep(.ant-card-body) {
  padding: 24px;
}

/* 按钮组优化 */
.basic-settings-container :deep(.ant-input-group-addon) {
  padding: 0;
}
</style>
