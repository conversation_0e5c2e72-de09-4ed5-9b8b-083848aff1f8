// 简化的 Temu 检测服务
// 专门负责检测 Temu 登录状态和获取店铺信息

import { getTemuApiUrl } from '../config'

// Temu 登录状态
interface TemuLoginStatus {
  isLoggedIn: boolean
  userInfo?: {
    userId: string
    userName?: string
    email?: string
  }
  shopInfo?: {
    mallId: string
    shopId: string
    mallName: string
    shopName: string
  }
  message?: string
  error?: string
}

// Temu 店铺信息
interface TemuShopData {
  success: boolean
  data?: any
  error?: string
}

class TemuDetectionService {
  // 检测 Temu 登录状态（主要方法）
  async checkTemuLoginStatus(): Promise<TemuLoginStatus> {
    try {
      console.info('[TemuDetection] 开始检测 Temu 登录状态...')

      // 0. 首先尝试从 Chrome Storage 中读取已保存的数据
      try {
        const result = await chrome.storage.local.get(['temuSiteInfo', 'shop_binding_state'])
        if (result.temuSiteInfo && result.shop_binding_state?.isTemuBound) {
          console.info('[TemuDetection] 从存储中找到已保存的 Temu 信息:', result.temuSiteInfo)
          return {
            isLoggedIn: true,
            userInfo: {
              userId: 'from_storage',
              userName: result.temuSiteInfo.shopName || result.temuSiteInfo.mallName
            },
            shopInfo: {
              mallId: String(result.temuSiteInfo.mallId),
              shopId: String(result.temuSiteInfo.shopId),
              mallName: result.temuSiteInfo.mallName,
              shopName: result.temuSiteInfo.shopName
            },
            message: '已登录 Temu 商家后台（从存储读取）'
          }
        }
      } catch (storageError) {
        console.warn('[TemuDetection] 读取存储失败，继续使用注入脚本检测:', storageError)
      }

      // 1. 查找 Temu 商家后台标签页
      const temuTab = await this.findTemuSellerTab()
      if (!temuTab) {
        console.info('[TemuDetection] 未找到 Temu 商家后台标签页')
        return {
          isLoggedIn: false,
          message: '未找到 Temu 商家后台标签页，请先打开并登录 Temu 商家后台'
        }
      }

      console.info('[TemuDetection] 找到 Temu 标签页:', temuTab.url)

      // 2. 向 Temu 标签页注入脚本检测登录状态
      try {
        // 检查 chrome.scripting API 是否可用
        if (!chrome.scripting || !chrome.scripting.executeScript) {
          throw new Error('chrome.scripting API 不可用，请检查扩展权限配置')
        }

        const results = await chrome.scripting.executeScript({
          target: { tabId: temuTab.id! },
          func: () => {
            // 在页面中检测登录状态的函数
            return (async () => {
              try {
                console.info('[Injected Script - Login Check] 开始检测登录状态...')

                // 获取 anti-content 头部
                const getAntiContent = () => {
                  try {
                    const win = window as any
                    if (win.antiContent) return win.antiContent
                    if (win.__INITIAL_STATE__?.antiContent) return win.__INITIAL_STATE__.antiContent

                    const scripts = Array.from(document.querySelectorAll('script'))
                    for (const script of scripts) {
                      const content = script.textContent || script.innerHTML
                      const patterns = [
                        /["']anti-content["']\s*:\s*["']([^"']+)["']/i,
                        /antiContent\s*:\s*["']([^"']+)["']/i,
                        /"anti-content":\s*"([^"]+)"/i
                      ]

                      for (const pattern of patterns) {
                        const match = content.match(pattern)
                        if (match) return match[1]
                      }
                    }
                    return null
                  } catch (error) {
                    return null
                  }
                }

                const antiContent = getAntiContent()
                const headers: Record<string, string> = {
                  'Accept': '*/*',
                  'Accept-Language': 'zh-CN,zh;q=0.9',
                  'Content-Type': 'application/json',
                  'Cache-Control': 'max-age=0'
                }

                if (antiContent) {
                  headers['anti-content'] = antiContent
                }

                const response = await fetch('https://seller.kuajingmaihuo.com/bg/quiet/api/mms/userInfo', {
                  method: 'POST',
                  credentials: 'include',
                  headers,
                  body: JSON.stringify({})
                })

                if (response.ok) {
                  const data = await response.json()
                  console.info('[Injected Script - Login Check] API 响应状态:', response.status)
                  console.info('[Injected Script - Login Check] API 响应数据:', data)

                  if (data.success && data.result) {
                    const userInfo = {
                      userId: String(data.result.userId || 'unknown'),
                      userName: data.result.userName || '未知用户'
                    }

                    // 解析店铺信息
                    let shopInfo: { mallId: string; shopId: string; mallName: string; shopName: string } | undefined = undefined
                    if (data.result.companyList && Array.isArray(data.result.companyList)) {
                      for (const company of data.result.companyList) {
                        if (company && company.malInfoList && Array.isArray(company.malInfoList)) {
                          for (const mallInfo of company.malInfoList) {
                            if (mallInfo && mallInfo.mallId && mallInfo.mallName) {
                              shopInfo = {
                                mallId: String(mallInfo.mallId),
                                shopId: String(mallInfo.mallId),
                                mallName: String(mallInfo.mallName),
                                shopName: String(mallInfo.mallName)
                              }
                              break
                            }
                          }
                          if (shopInfo) break
                        }
                      }
                    }

                    if (userInfo) {
                      console.info('[Injected Script - Login Check] 检测成功，用户已登录:', userInfo)
                      return {
                        isLoggedIn: true,
                        userInfo,
                        shopInfo,
                        message: '已登录 Temu 商家后台'
                      }
                    } else {
                      console.warn('[Injected Script - Login Check] 用户信息为空，判断为未登录')
                    }
                  }

                  console.warn('[Injected Script - Login Check] API 返回数据异常，判断为未登录')
                  return {
                    isLoggedIn: false,
                    message: 'API 返回数据异常，可能未登录'
                  }
                } else {
                  return {
                    isLoggedIn: false,
                    message: `API 请求失败: ${response.status} ${response.statusText}`
                  }
                }
              } catch (error) {
                console.error('[Injected Script - Login Check] 检测过程中发生错误:', error)
                return {
                  isLoggedIn: false,
                  message: '检测过程中发生错误',
                  error: error instanceof Error ? error.message : '检测失败'
                }
              }
            })()
          }
        }).catch(error => {
          console.error('[TemuDetection] 登录检测脚本注入失败:', error)
          throw new Error(`登录检测脚本注入失败: ${error.message}`)
        })

        if (results && results[0] && results[0].result) {
          const result = await results[0].result // 注意这里需要 await，因为注入的函数返回 Promise
          console.info('[TemuDetection] 检测结果:', result)
          return result
        } else {
          return {
            isLoggedIn: false,
            message: '无法检测登录状态'
          }
        }
      } catch (error) {
        console.error('[TemuDetection] 注入脚本失败:', error)
        return {
          isLoggedIn: false,
          message: '检测登录状态时发生错误',
          error: error instanceof Error ? error.message : '注入脚本失败'
        }
      }
    } catch (error) {
      console.error('[TemuDetection] 检测登录状态失败:', error)
      return {
        isLoggedIn: false,
        message: '检测登录状态时发生错误',
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  // 获取 Temu 店铺数据（用于绑定）
  async getTemuShopData(): Promise<TemuShopData> {
    try {
      console.info('[TemuDetection] 开始获取 Temu 店铺数据...')

      // 1. 查找 Temu 商家后台标签页
      const temuTab = await this.findTemuSellerTab()
      if (!temuTab) {
        return {
          success: false,
          error: '未找到 Temu 商家后台标签页'
        }
      }

      // 2. 向 Temu 标签页注入脚本获取数据
      try {
        // 检查 chrome.scripting API 是否可用
        if (!chrome.scripting || !chrome.scripting.executeScript) {
          throw new Error('chrome.scripting API 不可用，请检查扩展权限配置')
        }

        const results = await chrome.scripting.executeScript({
          target: { tabId: temuTab.id! },
          func: () => {
            // 在页面中获取店铺数据的函数
            return (async () => {
              try {
                console.info('[Injected Script] 开始获取店铺数据...')

                // 获取 anti-content 头部
                const getAntiContent = () => {
                  try {
                    const win = window as any
                    if (win.antiContent) return win.antiContent
                    if (win.__INITIAL_STATE__?.antiContent) return win.__INITIAL_STATE__.antiContent

                    const scripts = Array.from(document.querySelectorAll('script'))
                    for (const script of scripts) {
                      const content = script.textContent || script.innerHTML
                      const patterns = [
                        /["']anti-content["']\s*:\s*["']([^"']+)["']/i,
                        /antiContent\s*:\s*["']([^"']+)["']/i,
                        /"anti-content":\s*"([^"]+)"/i
                      ]

                      for (const pattern of patterns) {
                        const match = content.match(pattern)
                        if (match) return match[1]
                      }
                    }
                    return null
                  } catch (error) {
                    return null
                  }
                }

                const antiContent = getAntiContent()
                const headers: Record<string, string> = {
                  'Accept': '*/*',
                  'Accept-Language': 'zh-CN,zh;q=0.9',
                  'Content-Type': 'application/json',
                  'Cache-Control': 'max-age=0'
                }

                if (antiContent) {
                  headers['anti-content'] = antiContent
                }

                const response = await fetch('https://seller.kuajingmaihuo.com/bg/quiet/api/mms/userInfo', {
                  method: 'POST',
                  credentials: 'include',
                  headers,
                  body: JSON.stringify({})
                })

                if (response.ok) {
                  const data = await response.json()
                  console.info('[Injected Script] 获取店铺数据成功:', data)

                  if (data.success && data.result) {
                    return {
                      success: true,
                      data: data.result
                    }
                  } else {
                    return {
                      success: false,
                      error: 'API 返回数据格式错误'
                    }
                  }
                } else {
                  return {
                    success: false,
                    error: `API 请求失败: ${response.status} ${response.statusText}`
                  }
                }
              } catch (error) {
                console.error('[Injected Script] 获取店铺数据失败:', error)
                return {
                  success: false,
                  error: error instanceof Error ? error.message : '获取失败'
                }
              }
            })()
          }
        }).catch(error => {
          console.error('[TemuDetection] 脚本注入失败:', error)
          throw new Error(`脚本注入失败: ${error.message}`)
        })

        if (results && results[0] && results[0].result) {
          // 注入的函数返回一个 Promise，需要等待它完成
          const result = await results[0].result
          console.info('[TemuDetection] 获取店铺数据成功:', result)
          return result
        } else {
          console.warn('[TemuDetection] 注入脚本执行结果:', results)
          return {
            success: false,
            error: '注入脚本无返回结果'
          }
        }
      } catch (error) {
        console.error('[TemuDetection] 注入脚本失败:', error)
        return {
          success: false,
          error: error instanceof Error ? error.message : '注入脚本失败'
        }
      }
    } catch (error) {
      console.error('[TemuDetection] 获取店铺数据失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取失败'
      }
    }
  }

  // 查找 Temu 商家后台标签页
  private async findTemuSellerTab(): Promise<chrome.tabs.Tab | null> {
    const tabs = await chrome.tabs.query({})
    
    const temuDomains = [
      'seller.temu.com',
      'seller.kuajingmaihuo.com',
      'seller-cn.temu.com',
      'agentseller.temu.com',
      'agentseller-us.temu.com'
    ]

    for (const tab of tabs) {
      if (tab.url) {
        try {
          const url = new URL(tab.url)
          if (temuDomains.some(domain => url.hostname.includes(domain))) {
            return tab
          }
        } catch (error) {
          // 忽略无效 URL
          continue
        }
      }
    }

    return null
  }





  // 打开 Temu 商家后台
  openTemuBackend(): void {
    const temuUrl = 'https://seller.kuajingmaihuo.com/'
    window.open(temuUrl, '_blank')
  }
}

// 创建单例实例
export const temuDetectionService = new TemuDetectionService()
export default temuDetectionService

// 导出类型
export type { TemuLoginStatus, TemuShopData }
