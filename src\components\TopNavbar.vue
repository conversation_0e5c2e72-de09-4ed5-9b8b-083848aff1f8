<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuth } from '@/composables/useAuth'
import LoginModal from './LoginModal.vue'

// 菜单项配置 - 参考大卖家设计
const menuItems = ref([
  {
    id: 'dashboard',
    name: '工作台',
    icon: '🏠',
    route: '/side-panel/dashboard',
    active: true
  },
  {
    id: 'product-center',
    name: '上品中心',
    icon: '📊',
    route: '/side-panel/product-center',
    active: false
  },

  {
    id: 'forbidden-words',
    name: '违禁词库',
    icon: '⚠️',
    route: '/side-panel/forbidden-words',
    active: false
  },
  {
    id: 'member-service',
    name: '会员服务',
    icon: '👑',
    route: '/side-panel/member-service',
    active: false
  },
  
])

// 设置活跃菜单项
const setActiveMenuItem = (itemId: string) => {
  menuItems.value.forEach(item => {
    item.active = item.id === itemId
  })
}

// 获取路由对象
const route = useRoute()
const router = useRouter()

// 点击菜单项 - 使用 Vue Router 导航
const handleMenuClick = (item: { id: string; route: string }) => {
  setActiveMenuItem(item.id)
  // 使用 Vue Router 进行导航
  router.push(item.route)
}

// 使用登录状态管理
const { isLoggedIn, user, login, logout } = useAuth()

// 显示用户菜单
const showUserMenu = ref(false)

// 显示登录模态框
const showLoginModal = ref(false)

// 切换用户菜单
const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value
}

// 关闭用户菜单
const closeUserMenu = () => {
  showUserMenu.value = false
}

// 处理登录
const handleLogin = async (credentials: { username: string; password: string }) => {
  const result = await login(credentials)
  if (result.success) {
    showLoginModal.value = false
    alert('登录成功！')
  } else {
    alert(result.message)
  }
}

// 处理登出
const handleLogout = () => {
  logout()
  showUserMenu.value = false
  alert('已退出登录')
}

// 根据当前路由设置活跃菜单项
const updateActiveMenuItem = () => {
  if (!route || !route.path) return

  const currentPath = route.path

  // 找到匹配的菜单项
  const matchedItem = menuItems.value.find(item => item.route === currentPath)
  if (matchedItem) {
    setActiveMenuItem(matchedItem.id)
  }
}

// 监听路由变化
watch(() => route?.path, updateActiveMenuItem, { immediate: true })

// 组件挂载时设置活跃菜单项
onMounted(() => {
  updateActiveMenuItem()
})
</script>

<template>
  <a-layout-header class="bg-white border-b border-gray-200 shadow-sm sticky top-0 z-50 px-6 py-0 h-auto">
    <div class="flex items-center justify-between h-16">
      <!-- 左侧Logo和导航 -->
      <div class="flex items-center space-x-8">
        <!-- Logo -->
        <div class="flex items-center space-x-3">
          <a-avatar
            class="bg-blue-500"
            size="default"
          >
            胡
          </a-avatar>
          <div>
            <h1 class="font-bold text-gray-900 text-lg">胡建大卖家</h1>
          </div>
        </div>

        <!-- 主导航菜单 -->
        <nav class="hidden lg:flex">
          <a-space size="small">
            <a-button
              v-for="item in menuItems"
              :key="item.id"
              :type="item.active ? 'primary' : 'text'"
              size="default"
              class="flex items-center space-x-2 font-medium"
              @click="handleMenuClick(item)"
            >
              <span class="text-base">{{ item.icon }}</span>
              <span>{{ item.name }}</span>
            </a-button>
          </a-space>
        </nav>
      </div>

      <!-- 右侧用户信息和操作 -->
      <div class="flex items-center">
        <a-space size="middle">
          <!-- 积分显示 -->
          <a-tag
            v-if="isLoggedIn && user"
            color="blue"
            class="hidden md:flex items-center"
          >
            💎 {{ user.points }}
          </a-tag>

          <!-- 店铺信息 -->
          <a-tag
            v-if="isLoggedIn && user"
            color="default"
            class="hidden md:flex items-center"
          >
            🏪 {{ user.shopCount }}/{{ user.maxShops }}
          </a-tag>

          <!-- 用户菜单或登录按钮 -->
          <div class="relative">
            <!-- 已登录状态 -->
            <a-dropdown
              v-if="isLoggedIn && user"
              :trigger="['click']"
              placement="bottomRight"
            >
              <a-button type="text" class="flex items-center space-x-2">
                <a-avatar size="small" class="bg-gray-300">
                  👤
                </a-avatar>
                <div class="hidden md:block text-left">
                  <div class="text-sm font-medium">{{ user.username }}</div>
                  <div class="text-xs text-gray-500">{{ user.level }}</div>
                </div>
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item key="profile">
                    个人设置
                  </a-menu-item>
                  <a-menu-item key="upgrade">
                    会员升级
                  </a-menu-item>
                  <a-menu-item key="help">
                    帮助中心
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item key="logout" @click="handleLogout">
                    <span class="text-red-600">退出登录</span>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>

            <!-- 未登录状态 -->
            <a-button
              v-else
              type="primary"
              @click="showLoginModal = true"
            >
              登录
            </a-button>
          </div>
        </a-space>
      </div>
    </div>

    <!-- 移动端导航 -->
    <div class="lg:hidden border-t border-gray-200 px-4 py-2">
      <a-space size="small" wrap>
        <a-button
          v-for="item in menuItems"
          :key="item.id"
          :type="item.active ? 'primary' : 'text'"
          size="small"
          class="flex flex-col items-center"
          @click="handleMenuClick(item)"
        >
          <span class="text-base">{{ item.icon }}</span>
          <span class="text-xs">{{ item.name }}</span>
        </a-button>
      </a-space>
    </div>
  </a-layout-header>

  <!-- 登录模态框 -->
  <LoginModal
    :visible="showLoginModal"
    @close="showLoginModal = false"
    @login="handleLogin"
  />
</template>

<style scoped>
/* 确保下拉菜单在最上层 */
.z-50 {
  z-index: 50;
}
</style>
