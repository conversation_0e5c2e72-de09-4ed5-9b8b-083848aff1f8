<script setup lang="ts">
import { CheckCircleOutlined, RocketOutlined } from '@ant-design/icons-vue'

const displayName = __DISPLAY_NAME__
// const version = __VERSION__
</script>

<template>
  <div class="min-h-screen bg-gray-50 flex items-center justify-center">
    <a-card class="w-full max-w-md text-center shadow-lg" :bordered="false">
      <div class="space-y-6">
        <!-- 成功图标 -->
        <div class="flex justify-center">
          <a-avatar :size="80" class="bg-green-500">
            <template #icon>
              <CheckCircleOutlined class="text-3xl" />
            </template>
          </a-avatar>
        </div>

        <!-- 标题 -->
        <div>
          <h1 class="text-2xl font-bold text-gray-800 mb-2">
            🎉 安装成功！
          </h1>
          <p class="text-gray-600">
            感谢您安装 <strong>{{ displayName }}</strong>！❤️
          </p>
        </div>

        <!-- 说明 -->
        <a-alert
          type="success"
          show-icon
          message="安装完成"
          description="现在您可以关闭此标签页并开始使用扩展程序。"
        />

        <!-- 操作按钮 -->
        <div class="space-y-3">
          <a-button type="primary" size="large" block>
            <template #icon>
              <RocketOutlined />
            </template>
            开始使用
          </a-button>

          <a-button size="large" block @click="window.close()">
            关闭页面
          </a-button>
        </div>
      </div>
    </a-card>
  </div>
</template>
