<script setup lang="ts">
import { ref, computed } from 'vue'

// 定义 props 和 emits
const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  close: []
  login: [credentials: { username: string; password: string }]
}>()

// 表单数据
const loginForm = ref({
  username: '',
  password: '',
  agreeTerms: false
})

// 密码可见性
const passwordVisible = ref(false)

// 表单验证
const isFormValid = computed(() => {
  return loginForm.value.username.trim() !== '' && 
         loginForm.value.password.trim() !== '' && 
         loginForm.value.agreeTerms
})

// 切换密码可见性
const togglePasswordVisibility = () => {
  passwordVisible.value = !passwordVisible.value
}

// 处理登录
const handleLogin = () => {
  if (isFormValid.value) {
    emit('login', {
      username: loginForm.value.username,
      password: loginForm.value.password
    })
  }
}

// 关闭模态框
const handleClose = () => {
  emit('close')
}

// 重置表单
const resetForm = () => {
  loginForm.value = {
    username: '',
    password: '',
    agreeTerms: false
  }
  passwordVisible.value = false
}

// 监听 visible 变化，关闭时重置表单
import { watch } from 'vue'
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    resetForm()
  }
})
</script>

<template>
  <!-- 遮罩层 -->
  <div 
    v-if="visible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
    @click="handleClose"
  >
    <!-- 模态框内容 -->
    <div 
      class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4"
      @click.stop
    >
      <!-- 模态框头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">登录注册</h3>
        <button 
          @click="handleClose"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
          </svg>
        </button>
      </div>

      <!-- 模态框主体 -->
      <div class="p-6">
        <form @submit.prevent="handleLogin" class="space-y-6">
          <!-- 用户名输入 -->
          <div>
            <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
              手机号 <span class="text-red-500">*</span>
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <input
                id="username"
                v-model="loginForm.username"
                type="text"
                maxlength="11"
                placeholder="请输入手机号"
                class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
            </div>
          </div>

          <!-- 密码输入 -->
          <div>
            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
              密 码 <span class="text-red-500">*</span>
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <input
                id="password"
                v-model="loginForm.password"
                :type="passwordVisible ? 'text' : 'password'"
                maxlength="20"
                placeholder="请输入密码"
                class="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
              <button
                type="button"
                @click="togglePasswordVisibility"
                class="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                <svg v-if="passwordVisible" class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                  <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                </svg>
                <svg v-else class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clip-rule="evenodd"></path>
                  <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"></path>
                </svg>
              </button>
            </div>
          </div>

          <!-- 协议同意 -->
          <div class="flex items-center justify-center">
            <div class="flex items-center">
              <input
                id="agree-terms"
                v-model="loginForm.agreeTerms"
                type="checkbox"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              >
              <label for="agree-terms" class="ml-2 text-sm text-gray-600">
                我阅读并同意
                <a href="#" class="text-blue-600 hover:text-blue-800">用户协议</a>
                和
                <a href="#" class="text-blue-600 hover:text-blue-800">隐私政策</a>
              </label>
            </div>
          </div>

          <!-- 按钮组 -->
          <div class="flex flex-col space-y-3">
            <button
              type="submit"
              :disabled="!isFormValid"
              :class="[
                'w-full py-2 px-4 rounded-md font-medium transition-colors',
                isFormValid 
                  ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              ]"
            >
              登 录
            </button>
            
            <div class="flex justify-center space-x-4">
              <button
                type="button"
                class="text-blue-600 hover:text-blue-800 text-sm"
              >
                注册
              </button>
              <button
                type="button"
                class="text-blue-600 hover:text-blue-800 text-sm"
              >
                忘记密码
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 确保模态框在最上层 */
.z-50 {
  z-index: 50;
}
</style>
