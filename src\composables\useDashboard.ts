// Dashboard 数据管理
import { ref, computed, onMounted } from 'vue'
import { useShopBinding } from './useShopBinding'
import temuDataService from '../services/temuDataService'
import { useNotification } from './useNotification'

// 待办事项数据类型
interface TodoData {
  total: number
  details: {
    [key: string]: number
  }
}

// 商品数据类型
interface Product {
  id: number
  image: string
  title: string
  spu: string
  skc: string
  site: string
  currency: string
  category: string
  declaredPrice: string
  createTime: string
  sku: {
    image: string
    color: string
    itemNo: string
    status: string
    price: string
  }
}

// 兼容ProductData类型
type ProductData = Product

// 标签页数据类型
interface Tab {
  key: string
  label: string
  count: number
}

// 搜索表单类型
interface SearchForm {
  site: string
  productIdType: string
  productId: string
  skuType: string
  sku: string
}

export function useDashboard() {
  // 使用通知功能
  const { info } = useNotification()

  // 基础状态
  const isLoading = ref(false)
  const todoData = ref<TodoData>({ total: 0, details: {} })
  const products = ref<Product[]>([])
  const tabs = ref<Tab[]>([])
  const activeTab = ref('all')

  // 防重复调用标志
  const isInitialized = ref(false)
  
  // 分页相关
  const currentPage = ref(1)
  const pageSize = ref(20)
  const total = ref(0)
  
  // 搜索表单
  const searchForm = ref<SearchForm>({
    site: 'us',
    productIdType: 'SKC',
    productId: '',
    skuType: 'SKC',
    sku: ''
  })

  // 使用店铺绑定状态
  const { shopBinding } = useShopBinding()
  
  // 当前店铺信息
  const currentShopInfo = computed(() => {
    return shopBinding.value?.temuSiteInfo || null
  })

  // 店铺ID
  const mallId = computed(() => {
    return currentShopInfo.value?.mallId || currentShopInfo.value?.shopId || null
  })

  // 获取待办事项数量
  const fetchTodoCount = async () => {
    try {
      isLoading.value = true
      const result = await temuDataService.getTodoCount()
      
      if (result && typeof result === 'object') {
        // 处理待办事项数据
        const todoCount = (result as any).total || 0
        todoData.value = {
          total: todoCount,
          details: (result as any).details || {}
        }
      }
      
      return result
    } catch (error) {
      console.error('获取待办事项失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 获取商品列表
  const fetchProducts = async (customParams?: any) => {
    try {
      isLoading.value = true
      console.info('[useDashboard] 开始获取商品列表...')

      const params = customParams || {
        pageSize: pageSize.value,
        pageNum: currentPage.value,
        supplierTodoTypeList: []
      }

      console.info('[useDashboard] 请求参数:', params)
      const result = await temuDataService.getProductList(params)
      console.info('[useDashboard] API返回结果:', result)

      if (result && result.result) {
        // 格式化商品数据
        const formattedProducts = temuDataService.formatProductData(result)
        console.info('[useDashboard] 格式化后的商品数据:', formattedProducts)
        products.value = formattedProducts

        // 设置总数
        total.value = result.result.total || 0

        // 设置标签页数据
        const formattedTabs = temuDataService.formatStatusTabs(result)
        console.info('[useDashboard] 格式化后的标签页:', formattedTabs)
        tabs.value = formattedTabs
      }

      return result
    } catch (error) {
      console.error('[useDashboard] 获取商品列表失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 搜索商品
  const searchProducts = async () => {
    try {
      isLoading.value = true
      console.info('[useDashboard] 搜索参数:', searchForm.value)
      
      // 这里可以根据搜索条件调用不同的API
      const params = {
        pageSize: pageSize.value,
        pageNum: currentPage.value,
        supplierTodoTypeList: [],
        ...searchForm.value
      }
      
      const result = await temuDataService.getProductList(params)
      
      if (result && result.result) {
        const formattedProducts = temuDataService.formatProductData(result)
        products.value = formattedProducts
        total.value = result.result.total || 0
      }
      
      return result
    } catch (error) {
      console.error('搜索商品失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }



  // 切换标签页
  const switchTab = async (tabKey: string) => {
    console.info('[useDashboard] 切换标签页:', tabKey)
    activeTab.value = tabKey

    // 根据标签页设置过滤条件
    const params = {
      pageSize: pageSize.value,
      pageNum: 1,
      supplierTodoTypeList: [] as number[]
    }

    if (tabKey !== 'all') {
      const statusMap: { [key: string]: number[] } = {
        'selected': [1],
        'price-reporting': [7],
        'price-confirming': [9],
        'unpublished': [10],
        'published': [12],
        'offline': [13]
      }

      if (statusMap[tabKey]) {
        params.supplierTodoTypeList = statusMap[tabKey]
      }
    }

    // 重置页码
    currentPage.value = 1

    await fetchProducts(params)
  }

  // 处理分页变化
  const handlePageChange = async (page: number) => {
    console.info('[useDashboard] 页码变更:', page)
    currentPage.value = page
    await fetchProducts()
  }

  // 跳转到货源
  const jumpToSource = (product: ProductData) => {
    console.info('[useDashboard] 跳转货源:', product)
    // 这里可以实现跳转到货源页面的逻辑
    info('跳转', `正在跳转到商品 ${product.title} 的货源页面`)
  }

  // 刷新数据
  const refreshData = async () => {
    try {
      console.info('[useDashboard] 开始刷新数据...')
      await Promise.all([
        fetchTodoCount(),
        fetchProducts()
      ])
      console.info('[useDashboard] 数据刷新完成')
    } catch (error) {
      console.error('[useDashboard] 刷新数据失败:', error)
    }
  }

  // 组件挂载时初始化数据
  onMounted(() => {
    console.info('[useDashboard] 组件挂载，初始化数据...')

    // 防止重复初始化
    if (isInitialized.value) {
      console.info('[useDashboard] 已经初始化过，跳过')
      return
    }

    // 设置店铺ID到数据服务
    if (mallId.value) {
      console.info('[useDashboard] 设置店铺ID到数据服务:', mallId.value)
      temuDataService.setMallId(mallId.value.toString())
    }

    isInitialized.value = true
    refreshData()
  })

  // 初始化数据服务
  const initDataService = () => {
    console.info('[useDashboard] 初始化数据服务')
    // 这里可以添加数据服务初始化逻辑
  }

  return {
    // 状态
    isLoading,
    todoData,
    products,
    tabs,
    activeTab,
    currentPage,
    pageSize,
    total,
    searchForm,
    currentShopInfo,
    mallId,

    // 方法
    refreshData,
    fetchTodoCount,
    fetchProducts,
    searchProducts,
    switchTab,
    handlePageChange,
    jumpToSource,
    initDataService
  }
}
