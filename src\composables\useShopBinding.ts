import { ref, computed, readonly } from 'vue'
import shopBindingService from '../services/shopBindingService'

// 检查后端响应是否成功的辅助函数
const isApiSuccess = (response: any): boolean => {
  return response && response.code === 200
}

// Temu 店铺信息接口（参考大卖家插件格式）
interface TemuSiteInfo {
  fromPlat: string
  isSemiManagedMall: boolean
  logo: string
  mallId: number | string
  mallName: string
  mallStatus: number
  shopId: number | string
  shopName: string
}

// 店铺绑定状态接口
interface ShopBinding {
  isTemuBound: boolean
  temuSiteInfo?: TemuSiteInfo
  lastCheckTime?: string
}

// 全局状态
const shopBinding = ref<ShopBinding>({
  isTemuBound: false,
  temuSiteInfo: undefined,
  lastCheckTime: undefined
})

// 从 Chrome Extension Storage 恢复店铺绑定状态
const restoreShopBindingState = async () => {
  try {
    console.log('[useShopBinding] 开始恢复店铺绑定状态...')

    if (typeof chrome !== 'undefined' && chrome.storage) {
      // 使用 Chrome Extension Storage API
      const result = await chrome.storage.local.get(['temuSiteInfo', 'shop_binding_state'])
      console.log('[useShopBinding] Chrome Storage 中的数据:', result)

      if (result.temuSiteInfo) {
        // 直接使用 temuSiteInfo（参考大卖家插件格式）
        shopBinding.value = {
          isTemuBound: true,
          temuSiteInfo: result.temuSiteInfo,
          lastCheckTime: result.shop_binding_state?.lastCheckTime || new Date().toISOString()
        }
        console.log('[useShopBinding] 从 Chrome Storage 恢复店铺信息:', result.temuSiteInfo)
      } else if (result.shop_binding_state) {
        // 兼容旧格式
        shopBinding.value = { ...shopBinding.value, ...result.shop_binding_state }
        console.log('[useShopBinding] 从 Chrome Storage 恢复旧格式数据:', result.shop_binding_state)
      } else {
        console.log('[useShopBinding] Chrome Storage 中没有找到店铺数据')
      }
    } else {
      console.log('[useShopBinding] Chrome Storage 不可用，使用 localStorage')
      // 降级到 localStorage（开发环境或非扩展环境）
      const temuSiteInfo = localStorage.getItem('temuSiteInfo')
      const savedBinding = localStorage.getItem('shop_binding_state')

      if (temuSiteInfo) {
        try {
          const siteInfo = JSON.parse(temuSiteInfo)
          shopBinding.value = {
            isTemuBound: true,
            temuSiteInfo: siteInfo,
            lastCheckTime: new Date().toISOString()
          }
          console.log('[useShopBinding] 从 localStorage 恢复店铺信息:', siteInfo)
        } catch (error) {
          console.error('[useShopBinding] 解析 localStorage temuSiteInfo 失败:', error)
          localStorage.removeItem('temuSiteInfo')
        }
      } else if (savedBinding) {
        try {
          const bindingData = JSON.parse(savedBinding)
          shopBinding.value = { ...shopBinding.value, ...bindingData }
          console.log('[useShopBinding] 从 localStorage 恢复绑定状态:', bindingData)
        } catch (error) {
          console.error('[useShopBinding] 解析 localStorage shop_binding_state 失败:', error)
          localStorage.removeItem('shop_binding_state')
        }
      } else {
        console.log('[useShopBinding] localStorage 中没有找到店铺数据')
      }
    }

    console.log('[useShopBinding] 最终的店铺绑定状态:', shopBinding.value)
  } catch (error) {
    console.error('[useShopBinding] 恢复店铺绑定状态失败:', error)
  }
}

// 保存店铺绑定状态到 Chrome Extension Storage
const saveShopBindingState = async () => {
  try {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      // 使用 Chrome Extension Storage API
      const dataToSave: any = {
        shop_binding_state: shopBinding.value
      }

      // 如果有 temuSiteInfo，单独保存（参考大卖家插件格式）
      if (shopBinding.value.temuSiteInfo) {
        dataToSave.temuSiteInfo = shopBinding.value.temuSiteInfo
      }

      await chrome.storage.local.set(dataToSave)
      console.log('店铺信息已保存到 Chrome Storage:', dataToSave)
    } else {
      // 降级到 localStorage（开发环境或非扩展环境）
      localStorage.setItem('shop_binding_state', JSON.stringify(shopBinding.value))
    }
  } catch (error) {
    console.error('Failed to save shop binding state:', error)
  }
}

// 检测 Temu 店铺
const detectTemuShop = async (): Promise<{ success: boolean; shopInfo?: any; message: string }> => {
  try {
    // 检查当前页面是否为 Temu 商家后台
    const currentUrl = window.location.href
    const hostname = window.location.hostname

    // 更精确的 Temu 商家后台检测
    const temuSellerDomains = [
      'seller.temu.com',
      'seller.kuajingmaihuo.com',
      'seller-cn.temu.com',
      'agentseller.temu.com',
      'agentseller-us.temu.com'
    ]

    const isTemuDomain = temuSellerDomains.some(domain => hostname.includes(domain))
    const hasTemuSellerPath = currentUrl.includes('/seller') ||
                             currentUrl.includes('/agentseller') ||
                             currentUrl.includes('/mms') ||
                             currentUrl.includes('/bg-')

    if (isTemuDomain || hasTemuSellerPath) {
      // 尝试从页面中提取店铺信息
      let shopInfo = await extractShopInfoFromPage()

      if (!shopInfo) {
        // 如果无法提取店铺信息，返回失败
        return {
          success: false,
          message: '无法获取店铺信息，请确保在 Temu 商家后台页面并已登录'
        }
      }

      // 更新绑定状态（使用大卖家插件格式）
      const temuSiteInfo: TemuSiteInfo = {
        fromPlat: 'temu',
        isSemiManagedMall: shopInfo.isSemiManagedMall || false,
        logo: shopInfo.logo || '',
        mallId: shopInfo.mallId || shopInfo.shopId || 0,
        mallName: shopInfo.mallName || shopInfo.shopName || 'Unknown Shop',
        mallStatus: shopInfo.mallStatus || 1,
        shopId: shopInfo.shopId || shopInfo.mallId || 0,
        shopName: shopInfo.shopName || shopInfo.mallName || 'Unknown Shop'
      }

      shopBinding.value = {
        isTemuBound: true,
        temuSiteInfo: temuSiteInfo,
        lastCheckTime: new Date().toISOString()
      }

      await saveShopBindingState()

      return {
        success: true,
        shopInfo: temuSiteInfo,
        message: '成功检测到 Temu 店铺'
      }
    } else {
      return {
        success: false,
        message: '未检测到 Temu 店铺，请确保在 Temu 商家后台页面'
      }
    }
  } catch (error) {
    console.error('Error detecting Temu shop:', error)
    return {
      success: false,
      message: '检测店铺时发生错误'
    }
  }
}

// 从页面中提取店铺信息
const extractShopInfoFromPage = async (): Promise<any> => {
  try {
    // 方法1: 拦截网络请求获取店铺信息
    const shopInfoFromAPI = await interceptTemuAPI()
    if (shopInfoFromAPI) {
      return shopInfoFromAPI
    }

    // 方法2: 从页面的全局变量中获取
    const shopInfoFromGlobals = extractFromGlobalVariables()
    if (shopInfoFromGlobals) {
      return shopInfoFromGlobals
    }

    // 方法3: 从 localStorage/sessionStorage 中获取
    const shopInfoFromStorage = extractFromStorage()
    if (shopInfoFromStorage) {
      return shopInfoFromStorage
    }

    // 方法4: 从页面 DOM 元素中提取
    const shopInfoFromDOM = extractFromDOM()
    if (shopInfoFromDOM) {
      return shopInfoFromDOM
    }

    return null
  } catch (error) {
    console.error('Error extracting shop info:', error)
    return null
  }
}

// 拦截 Temu API 请求获取店铺信息
const interceptTemuAPI = async (): Promise<any> => {
  return new Promise((resolve) => {
    // 监听可能包含店铺信息的 API 请求
    const originalFetch = window.fetch
    const originalXHROpen = XMLHttpRequest.prototype.open

    let resolved = false
    const timeout = setTimeout(() => {
      if (!resolved) {
        resolved = true
        resolve(null)
      }
    }, 3000) // 3秒超时

    // 拦截 fetch 请求
    window.fetch = async function(...args) {
      const response = await originalFetch.apply(this, args)

      if (!resolved && response.url) {
        // 检查是否是店铺信息相关的 API
        const shopAPIPatterns = [
          '/userInfo',
          '/shopInfo',
          '/mallInfo',
          '/seller/info',
          '/mms/userInfo',
          '/bg/quiet/api/mms/userInfo',
          '/marvel-mms/cn/api/kiana'
        ]

        if (shopAPIPatterns.some(pattern => response.url.includes(pattern))) {
          try {
            const clonedResponse = response.clone()
            const data = await clonedResponse.json()

            if (data && (data.mallId || data.shopId || data.mallName)) {
              resolved = true
              clearTimeout(timeout)
              resolve({
                fromPlat: 'temu',
                mallId: data.mallId || data.shopId || 0,
                shopId: data.shopId || data.mallId || 0,
                mallName: data.mallName || data.shopName || 'Unknown Shop',
                shopName: data.shopName || data.mallName || 'Unknown Shop',
                mallStatus: data.mallStatus || 1,
                isSemiManagedMall: data.isSemiManagedMall || false,
                logo: data.logo || ''
              })
              return response
            }
          } catch (e) {
            // 忽略解析错误
          }
        }
      }

      return response
    }

    // 拦截 XMLHttpRequest
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
      const xhr = this

      if (!resolved && typeof url === 'string') {
        const shopAPIPatterns = [
          '/userInfo',
          '/shopInfo',
          '/mallInfo',
          '/seller/info',
          '/mms/userInfo',
          '/bg/quiet/api/mms/userInfo'
        ]

        if (shopAPIPatterns.some(pattern => url.includes(pattern))) {
          const originalOnLoad = xhr.onload
          xhr.onload = function() {
            try {
              if (xhr.responseText) {
                const data = JSON.parse(xhr.responseText)
                if (data && (data.mallId || data.shopId || data.mallName)) {
                  resolved = true
                  clearTimeout(timeout)
                  resolve({
                    fromPlat: 'temu',
                    mallId: data.mallId || data.shopId || 0,
                    shopId: data.shopId || data.mallId || 0,
                    mallName: data.mallName || data.shopName || 'Unknown Shop',
                    shopName: data.shopName || data.mallName || 'Unknown Shop',
                    mallStatus: data.mallStatus || 1,
                    isSemiManagedMall: data.isSemiManagedMall || false,
                    logo: data.logo || ''
                  })
                }
              }
            } catch (e) {
              // 忽略解析错误
            }

            if (originalOnLoad) {
              originalOnLoad.apply(this, arguments)
            }
          }
        }
      }

      return originalXHROpen.apply(this, [method, url, ...args])
    }

    // 恢复原始方法的函数
    setTimeout(() => {
      window.fetch = originalFetch
      XMLHttpRequest.prototype.open = originalXHROpen
    }, 5000)
  })
}

// 从全局变量中提取店铺信息
const extractFromGlobalVariables = (): any => {
  try {
    // 检查常见的全局变量
    const globalVars = [
      'window.__INITIAL_STATE__',
      'window.__NUXT__',
      'window.__NEXT_DATA__',
      'window.userInfo',
      'window.shopInfo',
      'window.mallInfo',
      'window.sellerInfo'
    ]

    for (const varPath of globalVars) {
      const value = getNestedProperty(window, varPath.replace('window.', ''))
      if (value && (value.mallId || value.shopId || value.mallName)) {
        return {
          fromPlat: 'temu',
          mallId: value.mallId || value.shopId || 0,
          shopId: value.shopId || value.mallId || 0,
          mallName: value.mallName || value.shopName || 'Unknown Shop',
          shopName: value.shopName || value.mallName || 'Unknown Shop',
          mallStatus: value.mallStatus || 1,
          isSemiManagedMall: value.isSemiManagedMall || false,
          logo: value.logo || ''
        }
      }
    }

    return null
  } catch (error) {
    console.error('Error extracting from global variables:', error)
    return null
  }
}

// 从存储中提取店铺信息
const extractFromStorage = (): any => {
  try {
    const storageKeys = [
      'userInfo', 'shopInfo', 'mallInfo', 'sellerInfo',
      'user', 'shop', 'mall', 'seller',
      'temu_user', 'temu_shop', 'temu_mall'
    ]

    for (const key of storageKeys) {
      const data = localStorage.getItem(key) || sessionStorage.getItem(key)
      if (data) {
        try {
          const parsed = JSON.parse(data)
          if (parsed && (parsed.mallId || parsed.shopId || parsed.mallName)) {
            return {
              fromPlat: 'temu',
              mallId: parsed.mallId || parsed.shopId || 0,
              shopId: parsed.shopId || parsed.mallId || 0,
              mallName: parsed.mallName || parsed.shopName || 'Unknown Shop',
              shopName: parsed.shopName || parsed.mallName || 'Unknown Shop',
              mallStatus: parsed.mallStatus || 1,
              isSemiManagedMall: parsed.isSemiManagedMall || false,
              logo: parsed.logo || ''
            }
          }
        } catch (e) {
          // 忽略解析错误
        }
      }
    }

    return null
  } catch (error) {
    console.error('Error extracting from storage:', error)
    return null
  }
}

// 从 DOM 元素中提取店铺信息
const extractFromDOM = (): any => {
  try {
    // 查找可能包含店铺信息的元素
    const selectors = [
      '[data-testid*="shop"]',
      '[data-testid*="mall"]',
      '[class*="shop-name"]',
      '[class*="mall-name"]',
      '[class*="seller-name"]',
      '.shop-info',
      '.mall-info',
      '.seller-info'
    ]

    for (const selector of selectors) {
      const elements = document.querySelectorAll(selector)
      for (const element of elements) {
        const text = element.textContent?.trim()
        if (text && text.length > 0 && text.length < 100) {
          // 找到可能的店铺名称，但需要更多信息才能构建完整的店铺信息
          console.info('[useShopBinding] 从DOM中找到可能的店铺名称:', text)
          // 返回 null，因为仅有名称不足以构建完整的店铺信息
          return null
        }
      }
    }

    return null
  } catch (error) {
    console.error('Error extracting from DOM:', error)
    return null
  }
}

// 获取嵌套属性的辅助函数
const getNestedProperty = (obj: any, path: string): any => {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : null
  }, obj)
}

// 手动绑定店铺
const bindTemuShop = (shopInfo: { shopId: string | number; shopName: string }) => {
  const temuSiteInfo: TemuSiteInfo = {
    fromPlat: 'temu',
    isSemiManagedMall: false,
    logo: '',
    mallId: shopInfo.shopId,
    mallName: shopInfo.shopName,
    mallStatus: 1,
    shopId: shopInfo.shopId,
    shopName: shopInfo.shopName
  }

  shopBinding.value = {
    isTemuBound: true,
    temuSiteInfo: temuSiteInfo,
    lastCheckTime: new Date().toISOString()
  }
  saveShopBindingState()
}

// 解绑店铺
const unbindTemuShop = async () => {
  shopBinding.value = {
    isTemuBound: false,
    temuSiteInfo: undefined,
    lastCheckTime: undefined
  }

  // 清除 Chrome Storage 中的数据
  if (typeof chrome !== 'undefined' && chrome.storage) {
    await chrome.storage.local.remove(['temuSiteInfo', 'shop_binding_state'])
  } else {
    localStorage.removeItem('shop_binding_state')
  }
}

// 打开 Temu 商家后台
const openTemuBackend = () => {
  // 根据地区和语言选择合适的 Temu 商家后台 URL
  const language = navigator.language || 'en'
  const isChineseUser = language.startsWith('zh')

  const temuUrls = isChineseUser ? [
    'https://seller.kuajingmaihuo.com',
    'https://seller-cn.temu.com',
    'https://seller.temu.com'
  ] : [
    'https://seller.temu.com',
    'https://agentseller.temu.com',
    'https://agentseller-us.temu.com'
  ]

  // 尝试打开最合适的 URL
  window.open(temuUrls[0], '_blank')

  // 提示用户
  console.log('正在打开 Temu 商家后台:', temuUrls[0])
}

// 主动调用 Temu API 获取店铺信息
const fetchTemuShopInfo = async (): Promise<{ success: boolean; shopInfo?: any; message: string }> => {
  try {
    console.log('[useShopBinding] 主动调用 Temu API 获取店铺信息...')

    // 调用 Temu 的用户信息 API
    const response = await fetch('https://seller.kuajingmaihuo.com/bg/quiet/api/mms/userInfo', {
      method: 'GET',
      credentials: 'include', // 包含 cookies
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    })

    if (response.ok) {
      const data = await response.json()
      console.log('[useShopBinding] API 返回数据:', data)

      // 解析 API 返回的数据
      if (data.code===200 && data.result && data.result.companyList) {
        for (const company of data.result.companyList) {
          if (company.malInfoList && Array.isArray(company.malInfoList)) {
            for (const mallInfo of company.malInfoList) {
              if (mallInfo.mallId && mallInfo.mallName) {
                // 构建 temuSiteInfo 格式
                const temuSiteInfo: TemuSiteInfo = {
                  fromPlat: 'temu',
                  isSemiManagedMall: mallInfo.isSemiManagedMall || false,
                  logo: mallInfo.logo || '',
                  mallId: mallInfo.mallId,
                  mallName: mallInfo.mallName,
                  mallStatus: mallInfo.mallStatus || 1,
                  shopId: mallInfo.mallId, // 在 Temu 中 mallId 就是 shopId
                  shopName: mallInfo.mallName
                }

                console.log('[useShopBinding] 构建的店铺信息:', temuSiteInfo)

                // 更新绑定状态
                shopBinding.value = {
                  isTemuBound: true,
                  temuSiteInfo: temuSiteInfo,
                  lastCheckTime: new Date().toISOString()
                }

                // 保存到存储
                try {
                  if (typeof chrome !== 'undefined' && chrome.storage) {
                    // 保存到 Chrome Storage
                    await chrome.storage.local.set({
                      temuSiteInfo: temuSiteInfo,
                      shop_binding_state: shopBinding.value
                    })
                    console.log('[useShopBinding] 数据已保存到 Chrome Storage')
                  } else {
                    // 降级到 localStorage
                    localStorage.setItem('temuSiteInfo', JSON.stringify(temuSiteInfo))
                    localStorage.setItem('shop_binding_state', JSON.stringify(shopBinding.value))
                    console.log('[useShopBinding] 数据已保存到 LocalStorage')
                  }
                } catch (storageError) {
                  console.error('[useShopBinding] 保存数据失败:', storageError)
                }

                return {
                  success: true,
                  shopInfo: temuSiteInfo,
                  message: '成功获取 Temu 店铺信息'
                }
              }
            }
          }
        }
      }

      console.log('[useShopBinding] API 返回数据中未找到店铺信息')
      return {
        success: false,
        message: 'API 返回数据中未找到店铺信息，请确保已登录 Temu 商家后台'
      }
    } else {
      console.log('[useShopBinding] API 调用失败:', response.status, response.statusText)
      return {
        success: false,
        message: `API 调用失败: ${response.status} ${response.statusText}`
      }
    }
  } catch (error) {
    console.error('[useShopBinding] API 调用出错:', error)
    return {
      success: false,
      message: '网络请求失败，请检查网络连接或确保已登录 Temu 商家后台'
    }
  }
}

// 绑定店铺到后端（新增）
const bindShopToBackend = async (shopInfo: TemuSiteInfo): Promise<{ success: boolean; message: string }> => {
  try {
    console.log('[useShopBinding] 开始绑定店铺到后端...')

    // 调用后端 API 绑定店铺
    const result = await shopBindingService.bindShop(shopInfo)

    if (isApiSuccess(result)) {
      // 绑定成功，更新本地状态
      shopBinding.value = {
        isTemuBound: true,
        temuSiteInfo: shopInfo,
        lastCheckTime: new Date().toISOString()
      }

      // 保存到本地存储
      await saveShopBindingState()

      console.log('[useShopBinding] 店铺绑定到后端成功')
      return {
        success: true,
        message: result.message || '店铺绑定成功'
      }
    } else {
      console.error('[useShopBinding] 店铺绑定到后端失败:', result.message)
      return {
        success: false,
        message: result.message || '绑定失败'
      }
    }
  } catch (error) {
    console.error('[useShopBinding] 绑定店铺到后端出错:', error)
    return {
      success: false,
      message: '绑定过程中发生错误'
    }
  }
}

// 从后端检查绑定状态（新增）
const checkBackendBindingStatus = async (): Promise<{ success: boolean; message: string; data?: any }> => {
  try {
    console.log('[useShopBinding] 检查后端绑定状态...')

    // 尝试从本地存储获取店铺信息
    const currentShopInfo = shopBinding.value.temuSiteInfo
    if (currentShopInfo && currentShopInfo.mallId) {
      shopBindingService.setShopId(String(currentShopInfo.mallId))
      console.info('[useShopBinding] 使用本地店铺ID:', currentShopInfo.mallId)
    } else {
      // 如果本地没有店铺信息，尝试从Chrome Storage恢复
      try {
        const result = await chrome.storage.local.get(['temuSiteInfo', 'shop_binding_state'])
        if (result.temuSiteInfo && result.temuSiteInfo.mallId) {
          shopBindingService.setShopId(String(result.temuSiteInfo.mallId))
          console.info('[useShopBinding] 从存储恢复店铺ID:', result.temuSiteInfo.mallId)

          // 更新本地状态
          shopBinding.value = {
            isTemuBound: result.shop_binding_state?.isTemuBound || true,
            temuSiteInfo: result.temuSiteInfo,
            lastCheckTime: result.shop_binding_state?.lastCheckTime || new Date().toISOString()
          }
        } else {
          console.warn('[useShopBinding] 本地和存储中都没有找到店铺信息')
          // 没有店铺信息时，仍然尝试检查后端状态
        }
      } catch (storageError) {
        console.warn('[useShopBinding] 从存储恢复店铺信息失败:', storageError)
      }
    }

    // 调用后端 API 检查绑定状态
    const result = await shopBindingService.checkBindingStatus()

    if (isApiSuccess(result) && result.data) {
      if (result.data.isTemuBound && result.data.temuSiteInfo) {
        // 更新本地状态
        shopBinding.value = {
          isTemuBound: true,
          temuSiteInfo: result.data.temuSiteInfo,
          lastCheckTime: result.data.lastCheckTime || new Date().toISOString()
        }

        // 保存到本地存储
        await saveShopBindingState()

        console.info('[useShopBinding] 从后端恢复绑定状态成功')
        return {
          success: true,
          message: '已恢复店铺绑定状态',
          data: result.data
        }
      } else {
        // 后端显示未绑定
        shopBinding.value = {
          isTemuBound: false,
          temuSiteInfo: undefined,
          lastCheckTime: undefined
        }

        return {
          success: true,
          message: '未找到绑定的店铺',
          data: result.data
        }
      }
    } else {
      console.error('[useShopBinding] 检查后端绑定状态失败:', result.message)
      return {
        success: false,
        message: result.message || '检查绑定状态失败'
      }
    }
  } catch (error) {
    console.error('[useShopBinding] 检查后端绑定状态出错:', error)
    return {
      success: false,
      message: '检查绑定状态时发生错误'
    }
  }
}

// 重新检测店铺
const recheckShop = async () => {
  // 首先尝试主动调用 API
  const apiResult = await fetchTemuShopInfo()
  if (apiResult.success) {
    return apiResult
  }

  // 如果 API 调用失败，使用原有的检测方法
  const result = await detectTemuShop()
  return result
}

// 计算属性
const isTemuBound = computed(() => shopBinding.value.isTemuBound)
const temuShopInfo = computed(() => shopBinding.value.temuSiteInfo)
const temuSiteInfo = computed(() => shopBinding.value.temuSiteInfo)

// 监听来自内容脚本的店铺检测消息
const setupMessageListener = () => {
  // 监听来自内容脚本的消息
  window.addEventListener('message', (event) => {
    if (event.data && event.data.type === 'TEMU_SHOP_DETECTED') {
      const shopInfo = event.data.data
      if (shopInfo && (shopInfo.mallId || shopInfo.shopId)) {
        // 构建 temuSiteInfo 格式
        const temuSiteInfo: TemuSiteInfo = {
          fromPlat: 'temu',
          isSemiManagedMall: shopInfo.isSemiManagedMall || false,
          logo: shopInfo.logo || '',
          mallId: shopInfo.mallId || shopInfo.shopId || 0,
          mallName: shopInfo.mallName || shopInfo.shopName || 'Unknown Shop',
          mallStatus: shopInfo.mallStatus || 1,
          shopId: shopInfo.shopId || shopInfo.mallId || 0,
          shopName: shopInfo.shopName || shopInfo.mallName || 'Unknown Shop'
        }

        // 更新绑定状态
        shopBinding.value = {
          isTemuBound: true,
          temuSiteInfo: temuSiteInfo,
          lastCheckTime: new Date().toISOString()
        }
        saveShopBindingState()
        console.info('从内容脚本接收到店铺信息:', temuSiteInfo)
      }
    }
  })

  // 监听来自背景脚本的消息
  if (typeof chrome !== 'undefined' && chrome.runtime) {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.type === 'TEMU_SHOP_DETECTED') {
        const shopInfo = message.data
        if (shopInfo && (shopInfo.mallId || shopInfo.shopId)) {
          // 构建 temuSiteInfo 格式
          const temuSiteInfo: TemuSiteInfo = {
            fromPlat: 'temu',
            isSemiManagedMall: shopInfo.isSemiManagedMall || false,
            logo: shopInfo.logo || '',
            mallId: shopInfo.mallId || shopInfo.shopId || 0,
            mallName: shopInfo.mallName || shopInfo.shopName || 'Unknown Shop',
            mallStatus: shopInfo.mallStatus || 1,
            shopId: shopInfo.shopId || shopInfo.mallId || 0,
            shopName: shopInfo.shopName || shopInfo.mallName || 'Unknown Shop'
          }

          // 更新绑定状态
          shopBinding.value = {
            isTemuBound: true,
            temuSiteInfo: temuSiteInfo,
            lastCheckTime: new Date().toISOString()
          }
          saveShopBindingState()
          console.info('从背景脚本接收到店铺信息:', temuSiteInfo)
          sendResponse({ success: true })
          return true // 表示异步响应
        }
      }
      return false // 表示不处理此消息
    })
  }
}

// 初始化时恢复状态并设置消息监听
restoreShopBindingState()
setupMessageListener()

export function useShopBinding() {
  return {
    // 状态
    isTemuBound,
    temuShopInfo,
    temuSiteInfo,
    shopBinding: readonly(shopBinding),

    // 方法
    detectTemuShop,
    fetchTemuShopInfo,
    bindTemuShop,
    unbindTemuShop,
    openTemuBackend,
    recheckShop,
    restoreShopBindingState,
    setupMessageListener,

    // 新增的后端集成方法
    bindShopToBackend,
    checkBackendBindingStatus
  }
}
