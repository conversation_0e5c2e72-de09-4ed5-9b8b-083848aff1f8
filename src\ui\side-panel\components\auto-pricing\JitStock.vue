<script setup lang="ts">
import { ref, computed } from 'vue'
import { PlayCircleOutlined } from '@ant-design/icons-vue'
import { getSiteOptions } from '../../../../config/temuSites'

// JIT&库存管理表单
const jitForm = ref({
  shop: '',
  site: '',
  jitEnabled: true,
  stockThreshold: 10,
  replenishmentQuantity: 100,
  leadTime: 7,
  safetyStock: 20,
  skcFilter: '',
  autoReplenishment: true
})

// 店铺选项
const shopOptions = [
  {
    value: 'ZenithCai',
    label: 'ZenithCai',
    avatar: 'https://img.cdnfe.com/supplier-public-tag/201365d418b/060d9c4f-71ff-4da0-8b98-c6c8a8221896_300x300.jpeg'
  }
]

// 站点选项
const siteOptions = computed(() => getSiteOptions().map(site => ({
  value: site.label,
  label: `${site.label}站`
})))

// 开始JIT库存管理
const startJitStock = () => {
  console.log('开始JIT库存管理:', jitForm.value)
  alert('JIT&库存管理功能已启动！')
}
</script>

<template>
  <div>
    <a-form
      :model="jitForm"
      layout="vertical"
      @finish="startJitStock"
      class="space-y-6"
    >
      <!-- 基础配置 -->
      <a-card title="基础配置" class="mb-6">
        <a-row :gutter="16">
          <!-- 店铺选择 -->
          <a-col :span="8">
            <a-form-item label="店铺">
              <a-select
                v-model:value="jitForm.shop"
                placeholder="选择店铺"
              >
                <a-select-option
                  v-for="shop in shopOptions"
                  :key="shop.value"
                  :value="shop.value"
                >
                  <div class="flex items-center space-x-2">
                    <img
                      :src="shop.avatar"
                      alt="店铺头像"
                      class="w-4 h-4 rounded"
                    />
                    <span>{{ shop.label }}</span>
                  </div>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 站点选择 -->
          <a-col :span="8">
            <a-form-item label="站点">
              <a-select
                v-model:value="jitForm.site"
                placeholder="选择站点"
              >
                <a-select-option
                  v-for="site in siteOptions"
                  :key="site.value"
                  :value="site.value"
                >
                  {{ site.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- JIT启用状态 -->
          <a-col :span="8">
            <a-form-item label="JIT状态">
              <a-switch
                v-model:checked="jitForm.jitEnabled"
                checked-children="启用"
                un-checked-children="禁用"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- SKC筛选 -->
        <a-form-item label="SKC筛选">
          <a-input
            v-model:value="jitForm.skcFilter"
            placeholder="多个查询请英文逗号、空格依次输入(选填)"
          />
        </a-form-item>
      </a-card>

      <!-- JIT参数设置 -->
      <a-card title="JIT参数设置" class="mb-6">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="库存阈值">
              <a-input-number
                v-model:value="jitForm.stockThreshold"
                :min="1"
                addon-after="件"
                class="w-full"
              />
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="补货数量">
              <a-input-number
                v-model:value="jitForm.replenishmentQuantity"
                :min="1"
                addon-after="件"
                class="w-full"
              />
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="交货周期">
              <a-input-number
                v-model:value="jitForm.leadTime"
                :min="1"
                addon-after="天"
                class="w-full"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="安全库存">
              <a-input-number
                v-model:value="jitForm.safetyStock"
                :min="0"
                addon-after="件"
                class="w-full"
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="自动补货">
              <a-switch
                v-model:checked="jitForm.autoReplenishment"
                checked-children="开启"
                un-checked-children="关闭"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>

      <!-- JIT说明 -->
      <a-card title="JIT说明" class="mb-6">
        <a-alert
          type="info"
          show-icon
          message="JIT(Just In Time)库存管理"
          description="实现精准的库存控制，减少库存积压，提高资金周转率。"
        />
        
        <div class="mt-4 space-y-2 text-sm text-gray-600">
          <div><strong>JIT原理：</strong></div>
          <div>• 当库存低于设定阈值时自动触发补货</div>
          <div>• 根据销售速度和交货周期计算最优库存</div>
          <div>• 保持安全库存以应对需求波动</div>
          <div>• 自动化补货流程，减少人工干预</div>
        </div>
      </a-card>

      <!-- 提交按钮 -->
      <div class="flex justify-end">
        <a-button 
          type="primary"
          html-type="submit"
          size="large"
        >
          <template #icon>
            <PlayCircleOutlined />
          </template>
          启动JIT管理
        </a-button>
      </div>
    </a-form>
  </div>
</template>
