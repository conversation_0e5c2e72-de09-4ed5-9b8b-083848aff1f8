/**
 * Temu经营站点配置
 * 基于docs/sp1.html分析和实际API调用确定的站点信息
 */

export interface TemuSite {
  id: number
  name: string
  code: string
  region: string
  currency: string
  language: string
  timezone: string
  status: 'active' | 'beta' | 'coming_soon'
}

// Temu经营站点配置
export const TEMU_SITES: Record<number, TemuSite> = {
  100: { 
    id: 100,
    name: '美国', 
    code: 'US', 
    region: '北美',
    currency: 'USD',
    language: 'en-US',
    timezone: 'America/New_York',
    status: 'active'
  },
  200: { 
    id: 200,
    name: '加拿大', 
    code: 'CA', 
    region: '北美',
    currency: 'CAD',
    language: 'en-CA',
    timezone: 'America/Toronto',
    status: 'active'
  },
  300: { 
    id: 300,
    name: '英国', 
    code: 'UK', 
    region: '欧洲',
    currency: 'GBP',
    language: 'en-GB',
    timezone: 'Europe/London',
    status: 'active'
  },
  400: { 
    id: 400,
    name: '德国', 
    code: 'DE', 
    region: '欧洲',
    currency: 'EUR',
    language: 'de-DE',
    timezone: 'Europe/Berlin',
    status: 'active'
  },
  500: { 
    id: 500,
    name: '法国', 
    code: 'FR', 
    region: '欧洲',
    currency: 'EUR',
    language: 'fr-FR',
    timezone: 'Europe/Paris',
    status: 'active'
  },
  600: { 
    id: 600,
    name: '意大利', 
    code: 'IT', 
    region: '欧洲',
    currency: 'EUR',
    language: 'it-IT',
    timezone: 'Europe/Rome',
    status: 'active'
  },
  700: { 
    id: 700,
    name: '西班牙', 
    code: 'ES', 
    region: '欧洲',
    currency: 'EUR',
    language: 'es-ES',
    timezone: 'Europe/Madrid',
    status: 'active'
  },
  800: { 
    id: 800,
    name: '澳大利亚', 
    code: 'AU', 
    region: '大洋洲',
    currency: 'AUD',
    language: 'en-AU',
    timezone: 'Australia/Sydney',
    status: 'active'
  }
}

// 获取所有活跃站点
export const getActiveSites = (): TemuSite[] => {
  return Object.values(TEMU_SITES).filter(site => site.status === 'active')
}

// 根据ID获取站点信息
export const getSiteById = (id: number): TemuSite | undefined => {
  return TEMU_SITES[id]
}

// 根据代码获取站点信息
export const getSiteByCode = (code: string): TemuSite | undefined => {
  return Object.values(TEMU_SITES).find(site => site.code === code)
}

// 根据名称获取站点信息
export const getSiteByName = (name: string): TemuSite | undefined => {
  return Object.values(TEMU_SITES).find(site => site.name === name)
}

// 按地区分组站点
export const getSitesByRegion = (): Record<string, TemuSite[]> => {
  const regions: Record<string, TemuSite[]> = {}
  
  Object.values(TEMU_SITES).forEach(site => {
    if (site.status === 'active') {
      if (!regions[site.region]) {
        regions[site.region] = []
      }
      regions[site.region].push(site)
    }
  })
  
  return regions
}

// 生成下拉选择选项
export const getSiteOptions = () => {
  return getActiveSites().map(site => ({
    value: site.id.toString(),
    label: site.name,
    code: site.code,
    region: site.region
  }))
}

// 生成分组下拉选择选项
export const getGroupedSiteOptions = () => {
  const regions = getSitesByRegion()
  const grouped: Array<{
    label: string
    options: Array<{
      value: string
      label: string
      code: string
    }>
  }> = []
  
  Object.entries(regions).forEach(([region, sites]) => {
    grouped.push({
      label: region,
      options: sites.map(site => ({
        value: site.id.toString(),
        label: site.name,
        code: site.code
      }))
    })
  })
  
  return grouped
}
