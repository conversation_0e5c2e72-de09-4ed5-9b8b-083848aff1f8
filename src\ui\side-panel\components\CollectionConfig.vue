<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

// Props
const props = defineProps<{
  collectionForm: {
    dataSource: string
    collectionUrl: string
    autoCollection: boolean
    collectionInterval: number
    maxItems: number
    filterRules: string
    keywords: string
    priceRange: {
      min: number | null
      max: number | null
    }
    categoryFilter: string
    ratingFilter: number
    salesFilter: number
    enableProxy: boolean
    proxyConfig: string
    collectImages: boolean
    collectReviews: boolean
    maxReviews: number
  }
}>()

// Emits
const emit = defineEmits<{
  'update:collectionForm': [value: typeof props.collectionForm]
  'start-collection': []
}>()

// 更新表单数据
const updateForm = (key: string, value: any) => {
  const newForm = { ...props.collectionForm, [key]: value }
  emit('update:collectionForm', newForm)
}

// 更新嵌套对象
const updateNestedForm = (parentKey: string, childKey: string, value: any) => {
  const newForm = {
    ...props.collectionForm,
    [parentKey]: {
      ...props.collectionForm[parentKey as keyof typeof props.collectionForm],
      [childKey]: value
    }
  }
  emit('update:collectionForm', newForm)
}

const handleStartCollection = () => {
  emit('start-collection')
}
</script>

<template>
  <div class="collection-config">
    <a-card 
      title="货盘采集" 
      class="shadow-sm"
      :bordered="false"
    >
      <template #extra>
        <a-tag color="orange">🌐 配置货盘采集规则和数据源</a-tag>
      </template>

      <a-form
        :model="collectionForm"
        layout="vertical"
        @finish="handleStartCollection"
        class="space-y-6"
      >
        <!-- 数据源设置 -->
        <a-card size="small" title="📊 数据源设置" class="mb-6">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item 
                label="数据源"
                name="dataSource"
                :rules="[{ required: true, message: '请选择数据源' }]"
              >
                <a-select
                  :value="collectionForm.dataSource"
                  @change="(value) => updateForm('dataSource', value)"
                  placeholder="请选择数据源"
                >
                  <a-select-option value="temu">
                    <div class="flex items-center">
                      <span class="mr-2">🛒</span>
                      Temu
                    </div>
                  </a-select-option>
                  <a-select-option value="amazon">
                    <div class="flex items-center">
                      <span class="mr-2">📦</span>
                      Amazon
                    </div>
                  </a-select-option>
                  <a-select-option value="aliexpress">
                    <div class="flex items-center">
                      <span class="mr-2">🌐</span>
                      AliExpress
                    </div>
                  </a-select-option>
                  <a-select-option value="1688">
                    <div class="flex items-center">
                      <span class="mr-2">🏭</span>
                      1688
                    </div>
                  </a-select-option>
                  <a-select-option value="taobao">
                    <div class="flex items-center">
                      <span class="mr-2">🛍️</span>
                      淘宝
                    </div>
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            
            <a-col :span="12">
              <a-form-item label="采集数量限制">
                <a-input-number
                  :value="collectionForm.maxItems"
                  @change="(value) => updateForm('maxItems', value)"
                  :min="1"
                  :max="1000"
                  placeholder="单次采集的最大商品数量"
                  style="width: 100%"
                >
                  <template #addonAfter>个商品</template>
                </a-input-number>
                <div class="mt-1 text-xs text-gray-500">单次采集的最大商品数量</div>
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item label="采集链接">
            <a-textarea
              :value="collectionForm.collectionUrl"
              @change="(e) => updateForm('collectionUrl', e.target.value)"
              :rows="3"
              placeholder="请输入要采集的商品链接或搜索页面链接，每行一个..."
            />
            <div class="mt-1 text-xs text-gray-500">
              支持商品详情页链接、搜索结果页链接、分类页链接等
            </div>
          </a-form-item>

          <a-form-item label="关键词搜索">
            <a-input
              :value="collectionForm.keywords"
              @change="(e) => updateForm('keywords', e.target.value)"
              placeholder="请输入搜索关键词，多个关键词用逗号分隔"
            />
            <div class="mt-1 text-xs text-gray-500">
              如：手机壳,保护套,iPhone配件
            </div>
          </a-form-item>
        </a-card>

        <!-- 筛选条件 -->
        <a-card size="small" title="🔍 筛选条件" class="mb-6">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="价格范围">
                <a-input-group compact>
                  <a-input-number
                    :value="collectionForm.priceRange.min"
                    @change="(value) => updateNestedForm('priceRange', 'min', value)"
                    :min="0"
                    :step="0.01"
                    placeholder="最低价"
                    style="width: 50%"
                  />
                  <a-input
                    style="width: 30px; border-left: 0; border-right: 0; pointer-events: none"
                    placeholder="~"
                    disabled
                  />
                  <a-input-number
                    :value="collectionForm.priceRange.max"
                    @change="(value) => updateNestedForm('priceRange', 'max', value)"
                    :min="0"
                    :step="0.01"
                    placeholder="最高价"
                    style="width: 50%; border-left: 0"
                  />
                </a-input-group>
              </a-form-item>
            </a-col>
            
            <a-col :span="12">
              <a-form-item label="最低评分">
                <a-input-number
                  :value="collectionForm.ratingFilter"
                  @change="(value) => updateForm('ratingFilter', value)"
                  :min="1"
                  :max="5"
                  :step="0.1"
                  placeholder="最低评分"
                  style="width: 100%"
                >
                  <template #addonAfter>星</template>
                </a-input-number>
                <div class="mt-1 text-xs text-gray-500">只采集评分高于此值的商品</div>
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item label="最低销量">
            <a-input-number
              :value="collectionForm.salesFilter"
              @change="(value) => updateForm('salesFilter', value)"
              :min="0"
              placeholder="最低销量"
              style="width: 200px"
            >
              <template #addonAfter>件</template>
            </a-input-number>
            <div class="mt-1 text-xs text-gray-500">只采集销量高于此值的商品</div>
          </a-form-item>
        </a-card>

        <!-- 自动采集设置 -->
        <a-card size="small" title="⏰ 自动采集设置" class="mb-6">
          <a-form-item label="自动采集">
            <a-switch
              :checked="collectionForm.autoCollection"
              @change="(checked) => updateForm('autoCollection', checked)"
              checked-children="启用"
              un-checked-children="关闭"
            />
          </a-form-item>
          
          <a-form-item v-if="collectionForm.autoCollection" label="采集间隔">
            <a-input-number
              :value="collectionForm.collectionInterval"
              @change="(value) => updateForm('collectionInterval', value)"
              :min="30"
              placeholder="采集间隔"
              style="width: 200px"
            >
              <template #addonAfter>分钟</template>
            </a-input-number>
            <div class="mt-1 text-xs text-gray-500">
              <ul class="list-disc list-inside space-y-1">
                <li>启用后，系统将按设定间隔自动执行采集任务</li>
                <li>建议间隔时间不少于30分钟，避免频繁请求被限制</li>
              </ul>
            </div>
          </a-form-item>
        </a-card>

        <!-- 采集内容选项 -->
        <a-card size="small" title="📋 采集内容" class="mb-6">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="基础内容">
                <a-space direction="vertical">
                  <a-switch
                    :checked="collectionForm.collectImages"
                    @change="(checked) => updateForm('collectImages', checked)"
                    checked-children="采集商品图片"
                    un-checked-children="不采集图片"
                  />
                  <a-switch
                    :checked="collectionForm.collectReviews"
                    @change="(checked) => updateForm('collectReviews', checked)"
                    checked-children="采集商品评价"
                    un-checked-children="不采集评价"
                  />
                </a-space>
              </a-form-item>
            </a-col>
            
            <a-col :span="12">
              <a-form-item v-if="collectionForm.collectReviews" label="评价数量">
                <a-input-number
                  :value="collectionForm.maxReviews"
                  @change="(value) => updateForm('maxReviews', value)"
                  :min="1"
                  :max="200"
                  placeholder="最多采集评价数"
                  style="width: 100%"
                >
                  <template #addonAfter>条评价</template>
                </a-input-number>
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>

        <!-- 代理设置 -->
        <a-card size="small" title="🔒 代理设置" class="mb-6">
          <a-form-item label="代理设置">
            <a-switch
              :checked="collectionForm.enableProxy"
              @change="(checked) => updateForm('enableProxy', checked)"
              checked-children="启用代理"
              un-checked-children="不使用代理"
            />
          </a-form-item>

          <a-form-item v-if="collectionForm.enableProxy" label="代理配置">
            <a-textarea
              :value="collectionForm.proxyConfig"
              @change="(e) => updateForm('proxyConfig', e.target.value)"
              :rows="3"
              placeholder="请输入代理配置，格式：http://username:<EMAIL>:8080"
            />
            <div class="mt-1 text-xs text-gray-500">
              <ul class="list-disc list-inside space-y-1">
                <li>支持HTTP/HTTPS/SOCKS5代理</li>
                <li>格式：协议://用户名:密码@代理地址:端口</li>
                <li>多个代理请换行分隔，系统将随机选择使用</li>
              </ul>
            </div>
          </a-form-item>
        </a-card>

        <!-- 高级设置 -->
        <a-card size="small" title="⚙️ 高级设置" class="mb-6">
          <a-form-item label="过滤规则">
            <a-textarea
              :value="collectionForm.filterRules"
              @change="(e) => updateForm('filterRules', e.target.value)"
              :rows="3"
              placeholder="请输入过滤规则，支持正则表达式..."
            />
            <div class="mt-1 text-xs text-gray-500">
              <ul class="list-disc list-inside space-y-1">
                <li>支持正则表达式过滤商品标题和描述</li>
                <li>每行一个规则，匹配的商品将被过滤掉</li>
                <li>例如：/测试|demo/i 将过滤包含"测试"或"demo"的商品</li>
              </ul>
            </div>
          </a-form-item>

          <a-form-item label="分类过滤">
            <a-input
              :value="collectionForm.categoryFilter"
              @change="(e) => updateForm('categoryFilter', e.target.value)"
              placeholder="请输入要过滤的分类关键词，多个用逗号分隔"
            />
            <div class="mt-1 text-xs text-gray-500">
              例如：成人用品,违禁品,危险品
            </div>
          </a-form-item>
        </a-card>

        <!-- 开始采集按钮 -->
        <a-form-item>
          <div class="flex justify-center space-x-4">
            <a-button
              type="primary"
              html-type="submit"
              size="large"
              class="px-8"
            >
              🚀 开始货盘采集
            </a-button>
            <a-button
              size="large"
              class="px-6"
              @click="() => {}"
            >
              📊 查看采集历史
            </a-button>
          </div>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<style scoped>
.collection-config :deep(.ant-form-item-label > label) {
  font-weight: 600;
}

.collection-config :deep(.ant-card-head-title) {
  font-size: 16px;
  font-weight: 600;
}

.collection-config :deep(.ant-card-small > .ant-card-head) {
  min-height: 48px;
}
</style>
