<script setup lang="ts">
import { ref, computed } from 'vue'
import { PlayCircleOutlined } from '@ant-design/icons-vue'
import { getSiteOptions } from '../../../../config/temuSites'

// 自动核价配置表单
const pricingForm = ref({
  shop: 'ZenithCai',
  site: '美国站',
  repeatCount: 1,
  pageSize: 100,
  skcFilter: '',
  // 同意核价条件
  stockThreshold: 10,
  profitMargin: 40,
  // 重新报价条件
  maxPricingAttempts: 4,
  priceReduction: 1,
  // 放弃核价条件
  abandonPricing: 1 // 1: 拒绝，放弃上新; 2: 不处理
})

// 店铺选项
const shopOptions = [
  {
    value: 'ZenithCai',
    label: 'ZenithCai',
    avatar: 'https://img.cdnfe.com/supplier-public-tag/201365d418b/060d9c4f-71ff-4da0-8b98-c6c8a8221896_300x300.jpeg'
  }
]

// 站点选项
const siteOptions = computed(() => getSiteOptions().map(site => ({
  value: site.label,
  label: `${site.label}站`
})))

// 分页数量选项
const pageSizeOptions = [
  { value: 20, label: '20' },
  { value: 50, label: '50' },
  { value: 100, label: '100' },
  { value: 200, label: '200' }
]

// 开始自动核价
const startAutoPricing = () => {
  console.log('开始自动核价:', pricingForm.value)
  alert('自动核价功能已启动！')
}
</script>

<template>
  <div>
    <a-form
      :model="pricingForm"
      layout="vertical"
      @finish="startAutoPricing"
      class="space-y-6"
    >
      <!-- 基础配置 -->
      <a-card title="基础配置" class="mb-6">
        <a-row :gutter="16">
          <!-- 店铺选择 -->
          <a-col :span="6">
            <a-form-item label="店铺">
              <a-select
                v-model:value="pricingForm.shop"
                placeholder="选择店铺"
              >
                <a-select-option
                  v-for="shop in shopOptions"
                  :key="shop.value"
                  :value="shop.value"
                >
                  <div class="flex items-center space-x-2">
                    <img
                      :src="shop.avatar"
                      alt="店铺头像"
                      class="w-4 h-4 rounded"
                    />
                    <span>{{ shop.label }}</span>
                  </div>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 站点选择 -->
          <a-col :span="6">
            <a-form-item label="站点">
              <a-select
                v-model:value="pricingForm.site"
                placeholder="选择站点"
              >
                <a-select-option
                  v-for="site in siteOptions"
                  :key="site.value"
                  :value="site.value"
                >
                  {{ site.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 重复执行次数 -->
          <a-col :span="6">
            <a-form-item label="重复执行次数">
              <a-input-number
                v-model:value="pricingForm.repeatCount"
                :min="1"
                class="w-full"
              />
            </a-form-item>
          </a-col>

          <!-- 分页数量 -->
          <a-col :span="6">
            <a-form-item label="分页数量">
              <a-select
                v-model:value="pricingForm.pageSize"
                placeholder="选择分页数量"
              >
                <a-select-option
                  v-for="size in pageSizeOptions"
                  :key="size.value"
                  :value="size.value"
                >
                  {{ size.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- SKC筛选 -->
        <a-form-item label="SKC筛选">
          <a-input
            v-model:value="pricingForm.skcFilter"
            placeholder="多个查询请英文逗号、空格依次输入(选填)"
          />
        </a-form-item>
      </a-card>

      <!-- 同意核价条件 -->
      <a-card title="同意核价条件" class="mb-6">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="货盘库存数">
              <a-input-number
                v-model:value="pricingForm.stockThreshold"
                :min="1"
                addon-before="≥"
                class="w-full"
              />
            </a-form-item>
          </a-col>
          
          <a-col :span="12">
            <a-form-item label="毛利率">
              <a-input-number
                v-model:value="pricingForm.profitMargin"
                :min="1"
                addon-before="≥"
                addon-after="%"
                class="w-full"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>

      <!-- 重新报价条件 -->
      <a-card title="重新报价条件" class="mb-6">
        <div class="space-y-4">
          <div class="text-sm text-gray-600 mb-4">
            不符合同意核价条件下的处理方式
          </div>
          
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="核价次数">
                <a-input-number
                  v-model:value="pricingForm.maxPricingAttempts"
                  :min="1"
                  addon-before="≤"
                  addon-after="次"
                  class="w-full"
                />
              </a-form-item>
            </a-col>
            
            <a-col :span="12">
              <a-form-item label="每次降价">
                <a-input-number
                  v-model:value="pricingForm.priceReduction"
                  :min="0.1"
                  :step="0.1"
                  addon-after="美元"
                  class="w-full"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-card>

      <!-- 放弃核价条件 -->
      <a-card title="放弃核价条件" class="mb-6">
        <div class="space-y-4">
          <div class="text-sm text-gray-600 mb-4">
            不符合同意核价条件以及重新报价条件下，是否放弃核价
          </div>
          
          <a-form-item>
            <a-radio-group v-model:value="pricingForm.abandonPricing">
              <a-radio :value="1">拒绝，放弃上新</a-radio>
              <a-radio :value="2">不处理</a-radio>
            </a-radio-group>
          </a-form-item>
        </div>
      </a-card>

      <!-- 提交按钮 -->
      <div class="flex justify-end">
        <a-button 
          type="primary"
          html-type="submit"
          size="large"
        >
          <template #icon>
            <PlayCircleOutlined />
          </template>
          开始自动核价
        </a-button>
      </div>
    </a-form>
  </div>
</template>
